use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use std::env;
use std::path::{Path, PathBuf};
use tokio::fs;

pub async fn run(package_path: Option<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    match package_path {
        Some(path) => link_package(&current_dir, &path, ui).await,
        None => create_global_link(&current_dir, ui).await,
    }
}

pub async fn run_unlink(package_name: Option<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    match package_name {
        Some(name) => unlink_package(&current_dir, &name, ui).await,
        None => remove_global_link(&current_dir, ui).await,
    }
}

async fn create_global_link(project_dir: &Path, ui: &UI) -> Result<()> {
    let package_json_path = project_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let package_name = package_json.name
        .ok_or_else(|| anyhow::anyhow!("Package name is required in package.json"))?;
    
    let global_links_dir = get_global_links_dir()?;
    fs::create_dir_all(&global_links_dir).await?;
    
    let link_path = global_links_dir.join(&package_name);
    
    // Remove existing link if it exists
    if link_path.exists() {
        fs::remove_file(&link_path).await?;
    }
    
    // Create symlink
    #[cfg(unix)]
    {
        use std::os::unix::fs::symlink;
        symlink(project_dir, &link_path)
            .context("Failed to create symlink")?;
    }
    
    #[cfg(windows)]
    {
        use std::os::windows::fs::symlink_dir;
        symlink_dir(project_dir, &link_path)
            .context("Failed to create symlink")?;
    }
    
    ui.success(&format!("✅ Linked {} globally", package_name));
    ui.info(&format!("Link created: {} -> {}", link_path.display(), project_dir.display()));
    
    Ok(())
}

async fn link_package(project_dir: &Path, package_path: &str, ui: &UI) -> Result<()> {
    let package_dir = if package_path.starts_with('/') || package_path.contains(':') {
        PathBuf::from(package_path)
    } else {
        project_dir.join(package_path)
    };
    
    if !package_dir.exists() {
        return Err(anyhow::anyhow!("Package directory does not exist: {}", package_dir.display()));
    }
    
    let package_json_path = package_dir.join("package.json");
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found in {}", package_dir.display()));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let package_name = package_json.name
        .ok_or_else(|| anyhow::anyhow!("Package name is required in package.json"))?;
    
    // Create node_modules if it doesn't exist
    let node_modules_dir = project_dir.join("node_modules");
    fs::create_dir_all(&node_modules_dir).await?;
    
    let link_path = node_modules_dir.join(&package_name);
    
    // Handle scoped packages
    if package_name.starts_with('@') {
        if let Some(scope_end) = package_name.find('/') {
            let scope_dir = node_modules_dir.join(&package_name[..scope_end]);
            fs::create_dir_all(&scope_dir).await?;
        }
    }
    
    // Remove existing link/directory if it exists
    if link_path.exists() {
        if link_path.is_dir() {
            fs::remove_dir_all(&link_path).await?;
        } else {
            fs::remove_file(&link_path).await?;
        }
    }
    
    // Create symlink
    #[cfg(unix)]
    {
        use std::os::unix::fs::symlink;
        symlink(&package_dir, &link_path)
            .context("Failed to create symlink")?;
    }
    
    #[cfg(windows)]
    {
        use std::os::windows::fs::symlink_dir;
        symlink_dir(&package_dir, &link_path)
            .context("Failed to create symlink")?;
    }
    
    ui.success(&format!("✅ Linked {} to project", package_name));
    ui.info(&format!("Link created: {} -> {}", link_path.display(), package_dir.display()));
    
    Ok(())
}

async fn remove_global_link(project_dir: &Path, ui: &UI) -> Result<()> {
    let package_json_path = project_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let package_name = package_json.name
        .ok_or_else(|| anyhow::anyhow!("Package name is required in package.json"))?;
    
    let global_links_dir = get_global_links_dir()?;
    let link_path = global_links_dir.join(&package_name);
    
    if !link_path.exists() {
        ui.warning(&format!("No global link found for {}", package_name));
        return Ok(());
    }
    
    fs::remove_file(&link_path).await
        .context("Failed to remove global link")?;
    
    ui.success(&format!("✅ Removed global link for {}", package_name));
    
    Ok(())
}

async fn unlink_package(project_dir: &Path, package_name: &str, ui: &UI) -> Result<()> {
    let node_modules_dir = project_dir.join("node_modules");
    let link_path = node_modules_dir.join(package_name);
    
    if !link_path.exists() {
        ui.warning(&format!("No link found for {}", package_name));
        return Ok(());
    }
    
    // Check if it's actually a symlink
    let metadata = fs::symlink_metadata(&link_path).await?;
    if !metadata.file_type().is_symlink() {
        return Err(anyhow::anyhow!("{} is not a symlink", package_name));
    }
    
    fs::remove_file(&link_path).await
        .context("Failed to remove link")?;
    
    ui.success(&format!("✅ Unlinked {}", package_name));
    
    Ok(())
}

fn get_global_links_dir() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .context("Could not find home directory")?;
    
    Ok(home_dir.join(".nx").join("links"))
}

pub async fn list_links(ui: &UI) -> Result<()> {
    let global_links_dir = get_global_links_dir()?;
    
    if !global_links_dir.exists() {
        ui.info("No global links found");
        return Ok(());
    }
    
    let mut links = Vec::new();
    let mut entries = fs::read_dir(&global_links_dir).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_symlink() {
            let name = entry.file_name().to_string_lossy().to_string();
            let target = fs::read_link(&path).await?;
            links.push((name, target.display().to_string()));
        }
    }
    
    if links.is_empty() {
        ui.info("No global links found");
        return Ok(());
    }
    
    let mut rows = Vec::new();
    for (name, target) in links {
        rows.push(vec![name, target]);
    }
    
    ui.print_table(&["Package", "Target"], &rows);
    
    Ok(())
}
