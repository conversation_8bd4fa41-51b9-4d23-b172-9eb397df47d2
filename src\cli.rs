use clap::{Parser, Subcommand};

#[derive(Parser)]
#[command(name = "nx")]
#[command(about = "Ultra-fast package manager for Node.js ecosystems")]
#[command(version = "1.0.0")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Install packages
    #[command(alias = "i")]
    Install {
        /// Packages to install
        packages: Vec<String>,
        /// Install globally
        #[arg(short, long)]
        global: bool,
        /// Save as dev dependency
        #[arg(short = 'D', long)]
        save_dev: bool,
        /// Save exact version
        #[arg(short = 'E', long)]
        save_exact: bool,
    },
    /// Uninstall packages
    #[command(alias = "rm")]
    Uninstall {
        /// Packages to uninstall
        packages: Vec<String>,
        /// Uninstall globally
        #[arg(short, long)]
        global: bool,
    },
    /// Update packages
    Update {
        /// Specific packages to update
        packages: Vec<String>,
    },
    /// Clean install from lockfile
    Ci,
    /// List installed packages
    #[command(alias = "ls")]
    List {
        /// List global packages
        #[arg(short, long)]
        global: bool,
        /// Maximum depth to show
        #[arg(short, long, default_value = "1")]
        depth: usize,
    },
    /// Show outdated packages
    Outdated,
    /// Show package information
    Info {
        /// Package name
        package: String,
    },
    /// Run security audit
    Audit {
        /// Automatically fix vulnerabilities
        #[arg(long)]
        fix: bool,
    },
    /// Run package script
    Run {
        /// Script name
        script: String,
        /// Script arguments
        args: Vec<String>,
    },
    /// Execute binary
    Exec {
        /// Command to execute
        command: String,
        /// Command arguments
        args: Vec<String>,
    },
    /// Rebuild native modules
    Rebuild,
    /// Remove duplicate dependencies
    Dedupe,
    /// Cache management
    Cache {
        /// Action (clear, stats)
        action: String,
    },
    /// Run diagnostics
    Doctor,
    /// Benchmark against other package managers
    Benchmark,
    /// Initialize a new project
    Init,
    /// Search for packages
    Search {
        /// Search query
        query: String,
        /// Maximum number of results
        #[arg(short, long, default_value = "20")]
        limit: usize,
    },
    /// Publish package to registry
    Publish {
        /// Distribution tag
        #[arg(long)]
        tag: Option<String>,
        /// Dry run (don't actually publish)
        #[arg(long)]
        dry_run: bool,
    },
    /// Link package for development
    Link {
        /// Path to package to link
        package_path: Option<String>,
    },
    /// Unlink package
    Unlink {
        /// Package name to unlink
        package_name: Option<String>,
    },
    /// List global links
    #[command(name = "list-links")]
    ListLinks,
    /// Configuration management
    Config {
        #[command(subcommand)]
        action: Option<ConfigAction>,
        /// Configuration key
        key: Option<String>,
        /// Configuration value
        value: Option<String>,
    },
    /// Workspace management
    Workspace {
        #[command(subcommand)]
        action: WorkspaceAction,
    },
    /// Version management
    Version {
        /// Version type (patch, minor, major, prerelease) or custom version
        version_type: Option<String>,
    },
    /// Clean project artifacts
    Clean,
    /// Registry management
    Registry {
        #[command(subcommand)]
        action: RegistryAction,
    },
    /// Security management
    Security {
        #[command(subcommand)]
        action: SecurityAction,
    },
    /// Environment management
    Env {
        #[command(subcommand)]
        action: EnvAction,
    },
    /// Script management
    Scripts {
        #[command(subcommand)]
        action: ScriptAction,
    },
    /// Migration tools
    Migrate {
        #[command(subcommand)]
        action: MigrateAction,
    },
}

#[derive(Subcommand)]
pub enum ConfigAction {
    /// Get configuration value
    Get,
    /// Set configuration value
    Set,
    /// Delete configuration key
    Delete,
    /// List all configuration
    List,
}

#[derive(Subcommand)]
pub enum WorkspaceAction {
    /// List workspaces
    List,
    /// Run script in workspaces
    Run {
        /// Script name
        script: String,
        /// Specific workspace
        #[arg(short, long)]
        workspace: Option<String>,
    },
    /// Install dependencies in workspaces
    Install {
        /// Specific workspace
        #[arg(short, long)]
        workspace: Option<String>,
    },
}

#[derive(Subcommand)]
pub enum RegistryAction {
    /// Login to registry
    Login {
        /// Registry URL
        #[arg(short, long)]
        registry: Option<String>,
    },
    /// Logout from registry
    Logout {
        /// Registry URL
        #[arg(short, long)]
        registry: Option<String>,
    },
    /// Show current user
    Whoami {
        /// Registry URL
        #[arg(short, long)]
        registry: Option<String>,
    },
    /// Add custom registry
    Add {
        /// Registry name
        name: String,
        /// Registry URL
        url: String,
    },
    /// Remove custom registry
    Remove {
        /// Registry name
        name: String,
    },
    /// List registries
    List,
}

#[derive(Subcommand)]
pub enum SecurityAction {
    /// Scan for vulnerabilities
    Scan,
    /// Check specific package
    Check {
        /// Package name
        package: String,
    },
    /// Update vulnerability database
    UpdateDb,
}

#[derive(Subcommand)]
pub enum EnvAction {
    /// List environment variables
    List,
    /// Set environment variable
    Set {
        /// Variable name
        key: String,
        /// Variable value
        value: String,
    },
    /// Get environment variable
    Get {
        /// Variable name
        key: String,
    },
    /// Unset environment variable
    Unset {
        /// Variable name
        key: String,
    },
    /// Validate environment configuration
    Validate,
    /// Generate .env.example
    Example,
}

#[derive(Subcommand)]
pub enum ScriptAction {
    /// List scripts
    List,
    /// Create new script
    Create {
        /// Script name
        name: String,
        /// Script command
        command: String,
    },
    /// Remove script
    Remove {
        /// Script name
        name: String,
    },
    /// Rename script
    Rename {
        /// Old script name
        old_name: String,
        /// New script name
        new_name: String,
    },
    /// Validate scripts
    Validate,
    /// Create from template
    Template {
        /// Template name
        template: String,
        /// Script name
        name: String,
    },
}

#[derive(Subcommand)]
pub enum MigrateAction {
    /// Migrate from npm
    FromNpm,
    /// Migrate from Yarn
    FromYarn,
    /// Migrate from pnpm
    FromPnpm,
}
