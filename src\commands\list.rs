use crate::resolver::Resolver;
use crate::ui::UI;
use anyhow::{Context, Result};
use semver::Version;
use std::collections::HashMap;
use std::env;
use std::path::Path;
use tokio::fs;

pub async fn run(global: bool, depth: usize, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    let node_modules_dir = if global {
        dirs::home_dir()
            .context("Could not find home directory")?
            .join(".nx")
            .join("global")
            .join("node_modules")
    } else {
        current_dir.join("node_modules")
    };

    if !node_modules_dir.exists() {
        ui.info("No packages installed");
        return Ok();
    }

    let packages = list_packages(&node_modules_dir, depth).await?;
    
    if packages.is_empty() {
        ui.info("No packages found");
        return Ok();
    }

    let mut rows = Vec::new();
    for (name, version, size) in packages {
        rows.push(vec![name, version, size]);
    }

    ui.print_table(&["Package", "Version", "Size"], &rows);
    
    Ok(())
}

pub async fn run_outdated(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }

    let spinner = ui.create_spinner("📊 Checking for outdated packages...");
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: crate::types::PackageJson = serde_json::from_str(&content)?;
    
    let resolver = Resolver::new()?;
    let mut outdated_packages = Vec::new();
    
    // Check dependencies
    if let Some(deps) = &package_json.dependencies {
        for (name, current_version) in deps {
            if let Ok(latest_version) = resolver.resolve_version(name, "latest").await {
                if let (Ok(current), Ok(latest)) = (Version::parse(current_version.trim_start_matches('^').trim_start_matches('~')), Version::parse(&latest_version)) {
                    if current < latest {
                        outdated_packages.push((name.clone(), current.to_string(), latest_version));
                    }
                }
            }
        }
    }
    
    // Check dev dependencies
    if let Some(dev_deps) = &package_json.dev_dependencies {
        for (name, current_version) in dev_deps {
            if let Ok(latest_version) = resolver.resolve_version(name, "latest").await {
                if let (Ok(current), Ok(latest)) = (Version::parse(current_version.trim_start_matches('^').trim_start_matches('~')), Version::parse(&latest_version)) {
                    if current < latest {
                        outdated_packages.push((name.clone(), current.to_string(), latest_version));
                    }
                }
            }
        }
    }
    
    spinner.finish_with_message("📊 Outdated check complete");
    
    if outdated_packages.is_empty() {
        ui.success("All packages are up to date!");
        return Ok();
    }

    let mut rows = Vec::new();
    for (name, current, latest) in outdated_packages {
        rows.push(vec![name, current, latest]);
    }

    ui.print_table(&["Package", "Current", "Latest"], &rows);
    
    Ok(())
}

async fn list_packages(node_modules_dir: &Path, _depth: usize) -> Result<Vec<(String, String, String)>> {
    let mut packages = Vec::new();
    
    let mut entries = fs::read_dir(node_modules_dir).await?;
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_dir() {
            let name = entry.file_name().to_string_lossy().to_string();
            
            // Skip hidden directories and .bin
            if name.starts_with('.') {
                continue;
            }
            
            let package_json_path = path.join("package.json");
            if package_json_path.exists() {
                let content = fs::read_to_string(&package_json_path).await?;
                if let Ok(package_json) = serde_json::from_str::<serde_json::Value>(&content) {
                    let version = package_json.get("version")
                        .and_then(|v| v.as_str())
                        .unwrap_or("unknown")
                        .to_string();
                    
                    // Calculate directory size
                    let size = calculate_directory_size(&path).await.unwrap_or(0);
                    let size_str = crate::utils::format_bytes(size);
                    
                    packages.push((name, version, size_str));
                }
            }
        }
    }
    
    packages.sort_by(|a, b| a.0.cmp(&b.0));
    Ok(packages)
}

async fn calculate_directory_size(dir: &Path) -> Result<u64> {
    let mut total_size = 0u64;
    let mut entries = fs::read_dir(dir).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_file() {
            if let Ok(metadata) = entry.metadata().await {
                total_size += metadata.len();
            }
        } else if path.is_dir() {
            total_size += calculate_directory_size(&path).await.unwrap_or(0);
        }
    }
    
    Ok(total_size)
}
