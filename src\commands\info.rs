use crate::resolver::Resolver;
use crate::ui::UI;
use anyhow::Result;

pub async fn run(package: String, ui: &UI) -> Result<()> {
    let spinner = ui.create_spinner(&format!("📋 Fetching info for {}...", package));
    
    let resolver = Resolver::new()?;
    let latest_version = resolver.resolve_version(&package, "latest").await?;
    let package_data = resolver.fetch_package_metadata(&package, &latest_version).await?;
    
    spinner.finish_with_message(&format!("📋 Package info for {}", package));

    // Calculate estimated size (this would need actual tarball size calculation)
    let estimated_size = estimate_package_size(&package_data);

    ui.print_package_info(
        &package_data.name,
        &package_data.version,
        &estimated_size,
        &package_data.license.unwrap_or_else(|| "Unknown".to_string()),
        package_data.dependencies.len(),
    );

    if let Some(description) = &package_data.description {
        println!("\nDescription: {}", description);
    }

    // Show dependencies
    if !package_data.dependencies.is_empty() {
        println!("\nDependencies:");
        for (name, version) in &package_data.dependencies {
            println!("  {} {}", name, version);
        }
    }

    // Show scripts
    if !package_data.scripts.is_empty() {
        println!("\nScripts:");
        for (name, command) in &package_data.scripts {
            println!("  {}: {}", name, command);
        }
    }

    Ok(())
}

fn estimate_package_size(package: &crate::types::Package) -> String {
    // This is a rough estimation based on dependency count
    // In a real implementation, you'd fetch the actual tarball size
    let base_size = 50_000; // 50KB base
    let dep_size = package.dependencies.len() * 10_000; // 10KB per dependency
    let total = base_size + dep_size;
    
    crate::utils::format_bytes(total as u64)
}
