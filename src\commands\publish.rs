use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use reqwest::Client;
use std::env;
use std::io::{self, Write};
use std::process::Command;
use tokio::fs;

pub async fn run(tag: Option<String>, dry_run: bool, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    // Read and validate package.json
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let package_name = package_json.name
        .ok_or_else(|| anyhow::anyhow!("Package name is required in package.json"))?;
    let package_version = package_json.version
        .ok_or_else(|| anyhow::anyhow!("Package version is required in package.json"))?;
    
    ui.info(&format!("📦 Publishing {}@{}", package_name, package_version));
    
    // Pre-publish checks
    run_pre_publish_checks(&current_dir, ui).await?;
    
    if dry_run {
        ui.info("🧪 Dry run mode - no actual publishing");
        return simulate_publish(&package_name, &package_version, ui).await;
    }
    
    // Check if user is logged in
    check_npm_auth(ui)?;
    
    // Check if version already exists
    if check_version_exists(&package_name, &package_version).await? {
        return Err(anyhow::anyhow!(
            "Version {} already exists for package {}",
            package_version,
            package_name
        ));
    }
    
    // Confirm publication
    if !confirm_publish(&package_name, &package_version)? {
        ui.info("Publication cancelled");
        return Ok(());
    }
    
    // Build package if build script exists
    if let Some(scripts) = &package_json.scripts {
        if scripts.contains_key("build") {
            ui.info("🔨 Running build script...");
            run_build_script(&current_dir)?;
        }
    }
    
    // Create tarball
    let spinner = ui.create_spinner("📦 Creating package tarball...");
    let tarball_path = create_tarball(&current_dir, &package_name, &package_version).await?;
    spinner.finish_with_message("📦 Tarball created");
    
    // Publish to registry
    let publish_spinner = ui.create_spinner("🚀 Publishing to registry...");
    publish_to_registry(&tarball_path, tag.as_deref()).await?;
    publish_spinner.finish_with_message("🚀 Published successfully");
    
    // Cleanup
    fs::remove_file(&tarball_path).await?;
    
    ui.success(&format!("✅ Successfully published {}@{}", package_name, package_version));
    ui.info(&format!("📋 View at: https://www.npmjs.com/package/{}", package_name));
    
    Ok(())
}

async fn run_pre_publish_checks(project_dir: &std::path::Path, ui: &UI) -> Result<()> {
    ui.info("🔍 Running pre-publish checks...");
    
    // Check for required files
    let required_files = ["package.json", "README.md"];
    for file in &required_files {
        if !project_dir.join(file).exists() {
            ui.warning(&format!("Missing recommended file: {}", file));
        }
    }
    
    // Check for .npmignore or files field
    let npmignore_exists = project_dir.join(".npmignore").exists();
    let gitignore_exists = project_dir.join(".gitignore").exists();
    
    if !npmignore_exists && !gitignore_exists {
        ui.warning("No .npmignore or .gitignore found - all files will be published");
    }
    
    // Run tests if test script exists
    let package_json_path = project_dir.join("package.json");
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    if let Some(scripts) = &package_json.scripts {
        if let Some(test_script) = scripts.get("test") {
            if test_script != "echo \"Error: no test specified\" && exit 1" {
                ui.info("🧪 Running tests...");
                let test_result = Command::new("npm")
                    .args(&["test"])
                    .current_dir(project_dir)
                    .status()
                    .context("Failed to run tests")?;
                
                if !test_result.success() {
                    return Err(anyhow::anyhow!("Tests failed - aborting publish"));
                }
                ui.success("✅ Tests passed");
            }
        }
    }
    
    Ok(())
}

fn check_npm_auth(ui: &UI) -> Result<()> {
    let output = Command::new("npm")
        .args(&["whoami"])
        .output()
        .context("Failed to check npm authentication")?;
    
    if !output.status.success() {
        ui.error("❌ Not logged in to npm");
        ui.info("Run 'npm login' to authenticate");
        return Err(anyhow::anyhow!("Not authenticated with npm registry"));
    }
    
    let username = String::from_utf8(output.stdout)
        .context("Invalid UTF-8 in npm whoami output")?
        .trim()
        .to_string();
    
    ui.success(&format!("✅ Authenticated as: {}", username));
    Ok(())
}

async fn check_version_exists(package_name: &str, version: &str) -> Result<bool> {
    let client = Client::new();
    let url = format!("https://registry.npmjs.org/{}/{}", package_name, version);
    
    let response = client.get(&url).send().await?;
    Ok(response.status().is_success())
}

fn confirm_publish(package_name: &str, version: &str) -> Result<bool> {
    print!("Publish {}@{} to npm registry? (y/N): ", package_name, version);
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    Ok(input.trim().to_lowercase().starts_with('y'))
}

fn run_build_script(project_dir: &std::path::Path) -> Result<()> {
    let status = Command::new("npm")
        .args(&["run", "build"])
        .current_dir(project_dir)
        .status()
        .context("Failed to run build script")?;
    
    if !status.success() {
        return Err(anyhow::anyhow!("Build script failed"));
    }
    
    Ok(())
}

async fn create_tarball(
    project_dir: &std::path::Path,
    package_name: &str,
    version: &str,
) -> Result<std::path::PathBuf> {
    let tarball_name = format!("{}-{}.tgz", package_name.replace('/', "-"), version);
    let tarball_path = project_dir.join(&tarball_name);
    
    let status = Command::new("npm")
        .args(&["pack", "--pack-destination", "."])
        .current_dir(project_dir)
        .status()
        .context("Failed to create tarball")?;
    
    if !status.success() {
        return Err(anyhow::anyhow!("Failed to create package tarball"));
    }
    
    Ok(tarball_path)
}

async fn publish_to_registry(tarball_path: &std::path::Path, tag: Option<&str>) -> Result<()> {
    let mut args = vec!["publish", tarball_path.to_str().unwrap()];
    
    if let Some(tag) = tag {
        args.extend(&["--tag", tag]);
    }
    
    let status = Command::new("npm")
        .args(&args)
        .status()
        .context("Failed to publish package")?;
    
    if !status.success() {
        return Err(anyhow::anyhow!("Failed to publish to registry"));
    }
    
    Ok(())
}

async fn simulate_publish(package_name: &str, version: &str, ui: &UI) -> Result<()> {
    ui.info("📋 Dry run simulation:");
    ui.info(&format!("  Package: {}", package_name));
    ui.info(&format!("  Version: {}", version));
    ui.info("  Files that would be published:");
    
    // Simulate file listing
    let files = vec![
        "package.json",
        "README.md",
        "index.js",
        "lib/",
        "dist/",
    ];
    
    for file in files {
        ui.info(&format!("    {}", file));
    }
    
    ui.success("✅ Dry run completed - no files were actually published");
    
    Ok(())
}
