use crate::ui::UI;
use anyhow::{Context, Result};
use std::env;
use std::process::Command;
use which::which;

pub async fn run(ui: &UI) -> Result<()> {
    ui.info("🩺 Running NX diagnostics...");
    
    let mut issues = Vec::new();
    let mut checks_passed = 0;
    let total_checks = 8;
    
    // Check Node.js installation
    ui.info("Checking Node.js installation...");
    match check_nodejs() {
        Ok(version) => {
            ui.success(&format!("✅ Node.js {} found", version));
            checks_passed += 1;
        }
        Err(e) => {
            ui.error(&format!("❌ Node.js check failed: {}", e));
            issues.push("Node.js is not installed or not in PATH".to_string());
        }
    }
    
    // Check npm installation
    ui.info("Checking npm installation...");
    match check_npm() {
        Ok(version) => {
            ui.success(&format!("✅ npm {} found", version));
            checks_passed += 1;
        }
        Err(e) => {
            ui.warning(&format!("⚠️ npm check failed: {}", e));
            issues.push("npm is not installed (optional for nx)".to_string());
        }
    }
    
    // Check cache directory
    ui.info("Checking cache directory...");
    match check_cache_directory().await {
        Ok(cache_info) => {
            ui.success(&format!("✅ Cache directory: {}", cache_info));
            checks_passed += 1;
        }
        Err(e) => {
            ui.error(&format!("❌ Cache check failed: {}", e));
            issues.push("Cache directory is not accessible".to_string());
        }
    }
    
    // Check permissions
    ui.info("Checking file permissions...");
    match check_permissions().await {
        Ok(_) => {
            ui.success("✅ File permissions OK");
            checks_passed += 1;
        }
        Err(e) => {
            ui.error(&format!("❌ Permission check failed: {}", e));
            issues.push("File permission issues detected".to_string());
        }
    }
    
    // Check network connectivity
    ui.info("Checking network connectivity...");
    match check_network_connectivity().await {
        Ok(_) => {
            ui.success("✅ Network connectivity OK");
            checks_passed += 1;
        }
        Err(e) => {
            ui.error(&format!("❌ Network check failed: {}", e));
            issues.push("Cannot connect to npm registry".to_string());
        }
    }
    
    // Check disk space
    ui.info("Checking disk space...");
    match check_disk_space().await {
        Ok(space_info) => {
            ui.success(&format!("✅ Disk space: {}", space_info));
            checks_passed += 1;
        }
        Err(e) => {
            ui.warning(&format!("⚠️ Disk space check: {}", e));
        }
    }
    
    // Check for common issues
    ui.info("Checking for common issues...");
    let common_issues = check_common_issues().await;
    if common_issues.is_empty() {
        ui.success("✅ No common issues detected");
        checks_passed += 1;
    } else {
        ui.warning(&format!("⚠️ Found {} potential issues", common_issues.len()));
        issues.extend(common_issues);
    }
    
    // Check environment variables
    ui.info("Checking environment variables...");
    match check_environment_variables() {
        Ok(env_info) => {
            ui.success(&format!("✅ Environment: {}", env_info));
            checks_passed += 1;
        }
        Err(e) => {
            ui.warning(&format!("⚠️ Environment check: {}", e));
        }
    }
    
    // Summary
    println!("\n📊 Diagnostic Summary");
    println!("Checks passed: {}/{}", checks_passed, total_checks);
    
    if issues.is_empty() {
        ui.success("🎉 All checks passed! NX is ready to use.");
    } else {
        ui.warning(&format!("Found {} issues:", issues.len()));
        for (i, issue) in issues.iter().enumerate() {
            println!("  {}. {}", i + 1, issue);
        }
        
        println!("\n💡 Recommendations:");
        print_recommendations(&issues, ui);
    }
    
    Ok(())
}

fn check_nodejs() -> Result<String> {
    let output = Command::new("node")
        .args(&["--version"])
        .output()
        .context("Failed to execute node --version")?;
    
    if output.status.success() {
        let version = String::from_utf8(output.stdout)
            .context("Invalid UTF-8 in node version output")?
            .trim()
            .to_string();
        Ok(version)
    } else {
        Err(anyhow::anyhow!("Node.js not found"))
    }
}

fn check_npm() -> Result<String> {
    let output = Command::new("npm")
        .args(&["--version"])
        .output()
        .context("Failed to execute npm --version")?;
    
    if output.status.success() {
        let version = String::from_utf8(output.stdout)
            .context("Invalid UTF-8 in npm version output")?
            .trim()
            .to_string();
        Ok(version)
    } else {
        Err(anyhow::anyhow!("npm not found"))
    }
}

async fn check_cache_directory() -> Result<String> {
    let cache_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx")
        .join("cache");
    
    if !cache_dir.exists() {
        tokio::fs::create_dir_all(&cache_dir).await
            .context("Failed to create cache directory")?;
    }
    
    // Check if we can write to the cache directory
    let test_file = cache_dir.join("test_write");
    tokio::fs::write(&test_file, "test").await
        .context("Cannot write to cache directory")?;
    tokio::fs::remove_file(&test_file).await
        .context("Cannot remove test file from cache directory")?;
    
    Ok(format!("{} (writable)", cache_dir.display()))
}

async fn check_permissions() -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    // Try to create a test file
    let test_file = current_dir.join(".nx_permission_test");
    tokio::fs::write(&test_file, "test").await
        .context("Cannot write to current directory")?;
    tokio::fs::remove_file(&test_file).await
        .context("Cannot remove test file")?;
    
    Ok(())
}

async fn check_network_connectivity() -> Result<()> {
    let client = reqwest::Client::builder()
        .timeout(std::time::Duration::from_secs(10))
        .build()
        .context("Failed to create HTTP client")?;
    
    let response = client
        .get("https://registry.npmjs.org/")
        .send()
        .await
        .context("Failed to connect to npm registry")?;
    
    if response.status().is_success() {
        Ok(())
    } else {
        Err(anyhow::anyhow!("npm registry returned status: {}", response.status()))
    }
}

async fn check_disk_space() -> Result<String> {
    // This is a simplified check - in a real implementation you'd use platform-specific APIs
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    // Try to get some basic info about available space
    // This is a placeholder - real implementation would use statvfs on Unix or GetDiskFreeSpace on Windows
    Ok("Available (detailed check not implemented)".to_string())
}

async fn check_common_issues() -> Vec<String> {
    let mut issues = Vec::new();
    
    // Check for node_modules in unusual places
    let current_dir = env::current_dir().unwrap_or_default();
    let node_modules = current_dir.join("node_modules");
    
    if node_modules.exists() {
        if let Ok(metadata) = tokio::fs::metadata(&node_modules).await {
            if metadata.len() > 1_000_000_000 { // > 1GB
                issues.push("Large node_modules directory detected (>1GB)".to_string());
            }
        }
    }
    
    // Check for conflicting package managers
    if current_dir.join("yarn.lock").exists() && current_dir.join("package-lock.json").exists() {
        issues.push("Multiple lockfiles detected (yarn.lock and package-lock.json)".to_string());
    }
    
    // Check for old Node.js version
    if let Ok(version_output) = Command::new("node").args(&["--version"]).output() {
        if let Ok(version_str) = String::from_utf8(version_output.stdout) {
            let version_str = version_str.trim().trim_start_matches('v');
            if let Ok(version) = semver::Version::parse(version_str) {
                if version.major < 16 {
                    issues.push(format!("Old Node.js version detected ({}), recommend >=16", version));
                }
            }
        }
    }
    
    issues
}

fn check_environment_variables() -> Result<String> {
    let mut env_info = Vec::new();
    
    if let Ok(node_env) = env::var("NODE_ENV") {
        env_info.push(format!("NODE_ENV={}", node_env));
    }
    
    if let Ok(npm_config_registry) = env::var("NPM_CONFIG_REGISTRY") {
        env_info.push(format!("Registry={}", npm_config_registry));
    }
    
    if env_info.is_empty() {
        Ok("Default environment".to_string())
    } else {
        Ok(env_info.join(", "))
    }
}

fn print_recommendations(issues: &[String], ui: &UI) {
    for issue in issues {
        if issue.contains("Node.js") {
            ui.info("  • Install Node.js from https://nodejs.org/");
        } else if issue.contains("npm registry") {
            ui.info("  • Check your internet connection");
            ui.info("  • Verify firewall/proxy settings");
        } else if issue.contains("cache") {
            ui.info("  • Run 'nx cache clear' to reset cache");
        } else if issue.contains("permissions") {
            ui.info("  • Check file/directory permissions");
            ui.info("  • Avoid using sudo with package managers");
        } else if issue.contains("lockfiles") {
            ui.info("  • Remove conflicting lockfiles");
            ui.info("  • Use only one package manager per project");
        } else if issue.contains("Old Node.js") {
            ui.info("  • Update Node.js to version 16 or higher");
        }
    }
}
