use nx::cache::Cache;

#[tokio::test]
async fn test_cache_set_and_get() {
    let cache = Cache::new().unwrap();
    let key = "test-key";
    let data = b"test data".to_vec();

    cache.set(key, data.clone()).await.unwrap();
    let retrieved = cache.get(key).await.unwrap();

    assert!(retrieved.is_some());
    assert_eq!(retrieved.unwrap(), data);
}

#[tokio::test]
async fn test_cache_miss() {
    let cache = Cache::new().unwrap();
    let result = cache.get("non-existent-key").await.unwrap();
    assert!(result.is_none());
}

#[tokio::test]
async fn test_cache_key_hashing() {
    let cache = Cache::new().unwrap();
    let hash1 = cache.hash_key("test");
    let hash2 = cache.hash_key("test");
    let hash3 = cache.hash_key("different");

    assert_eq!(hash1, hash2);
    assert_ne!(hash1, hash3);
    assert_eq!(hash1.len(), 64); // BLAKE3 produces 32-byte hashes (64 hex chars)
}
