use crate::cache::Cache;
use crate::types::{Package, RegistryResponse, ResolvedPackage};
use anyhow::{Context, Result};
use reqwest::Client;
use semver::{Version, VersionReq};
use std::collections::{HashMap, HashSet, VecDeque};
use std::sync::Arc;
use tokio::sync::Semaphore;

pub struct Resolver {
    client: Client,
    cache: Cache,
    registry_url: String,
    semaphore: Arc<Semaphore>,
}

impl Resolver {
    pub fn new() -> Result<Self> {
        let client = Client::builder()
            .pool_max_idle_per_host(10)
            .pool_idle_timeout(std::time::Duration::from_secs(30))
            .timeout(std::time::Duration::from_secs(30))
            .build()
            .context("Failed to create HTTP client")?;

        Ok(Self {
            client,
            cache: Cache::new()?,
            registry_url: "https://registry.npmjs.org".to_string(),
            semaphore: Arc::new(Semaphore::new(50)), // Limit concurrent requests
        })
    }

    pub async fn resolve_dependencies(&self, packages: &[(String, String)]) -> Result<Vec<ResolvedPackage>> {
        let mut resolved = Vec::new();
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();
        let mut dependency_graph = HashMap::new();

        // Add initial packages to queue
        for (name, version_req) in packages {
            queue.push_back((name.clone(), version_req.clone(), 0));
        }

        // Build dependency graph
        while let Some((name, version_req, depth)) = queue.pop_front() {
            let key = format!("{}@{}", name, version_req);
            if visited.contains(&key) {
                continue;
            }
            visited.insert(key.clone());

            // Resolve version
            let version = self.resolve_version(&name, &version_req).await?;
            let package = self.fetch_package_metadata(&name, &version).await?;

            // Store in dependency graph
            dependency_graph.insert(key.clone(), package.clone());

            // Add dependencies to queue (limit depth to prevent infinite recursion)
            if depth < 10 {
                for (dep_name, dep_version) in &package.dependencies {
                    queue.push_back((dep_name.clone(), dep_version.clone(), depth + 1));
                }
                
                // Also include peer dependencies if they exist
                for (dep_name, dep_version) in &package.peer_dependencies {
                    queue.push_back((dep_name.clone(), dep_version.clone(), depth + 1));
                }
            }
        }

        // Convert to resolved packages with proper dependency tree
        for (key, package) in dependency_graph {
            let mut dependencies = Vec::new();
            
            // Resolve child dependencies
            for (dep_name, dep_version) in &package.dependencies {
                let dep_key = format!("{}@{}", dep_name, dep_version);
                if let Some(dep_package) = dependency_graph.get(&dep_key) {
                    dependencies.push(ResolvedPackage {
                        name: dep_package.name.clone(),
                        version: dep_package.version.clone(),
                        resolved: dep_package.dist.tarball.clone(),
                        integrity: dep_package.dist.shasum.clone(),
                        dependencies: Vec::new(), // Flattened for simplicity
                    });
                }
            }

            resolved.push(ResolvedPackage {
                name: package.name.clone(),
                version: package.version.clone(),
                resolved: package.dist.tarball.clone(),
                integrity: package.dist.shasum.clone(),
                dependencies,
            });
        }

        // Remove duplicates and sort by dependency order
        self.deduplicate_and_sort(resolved)
    }

    pub async fn resolve_version(&self, name: &str, version_req: &str) -> Result<String> {
        if version_req == "latest" {
            return self.get_latest_version(name).await;
        }

        // Handle git URLs
        if version_req.starts_with("git+") || version_req.contains("github.com") {
            return Ok(version_req.to_string());
        }

        // Handle file paths
        if version_req.starts_with("file:") || version_req.starts_with("./") || version_req.starts_with("../") {
            return Ok(version_req.to_string());
        }

        // Handle tarball URLs
        if version_req.starts_with("http://") || version_req.starts_with("https://") {
            return Ok(version_req.to_string());
        }

        // Try to parse as exact version first
        if let Ok(version) = Version::parse(version_req) {
            return Ok(version.to_string());
        }

        // Parse as version requirement
        let req = VersionReq::parse(version_req)
            .context("Invalid version requirement")?;

        let registry_data = self.fetch_registry_data(name).await?;
        
        // Find the best matching version
        let mut versions: Vec<Version> = registry_data.versions.keys()
            .filter_map(|v| Version::parse(v).ok())
            .collect();
        
        versions.sort();
        versions.reverse(); // Start with highest version

        for version in versions {
            if req.matches(&version) {
                return Ok(version.to_string());
            }
        }

        Err(anyhow::anyhow!("No matching version found for {}@{}", name, version_req))
    }

    async fn get_latest_version(&self, name: &str) -> Result<String> {
        let registry_data = self.fetch_registry_data(name).await?;
        
        if let Some(latest) = registry_data.dist_tags.get("latest") {
            Ok(latest.clone())
        } else {
            // Fallback to highest version number
            let mut versions: Vec<Version> = registry_data.versions.keys()
                .filter_map(|v| Version::parse(v).ok())
                .collect();
            
            if versions.is_empty() {
                return Err(anyhow::anyhow!("No versions found for {}", name));
            }
            
            versions.sort();
            Ok(versions.last().unwrap().to_string())
        }
    }

    async fn fetch_registry_data(&self, name: &str) -> Result<RegistryResponse> {
        let cache_key = format!("registry:{}", name);
        
        // Try cache first
        if let Some(cached) = self.cache.get(&cache_key).await? {
            if let Ok(data) = serde_json::from_slice::<RegistryResponse>(&cached) {
                return Ok(data);
            }
        }

        // Fetch from registry
        let _permit = self.semaphore.acquire().await?;
        let url = format!("{}/{}", self.registry_url, urlencoding::encode(name));
        
        let response = self.client.get(&url)
            .header("Accept", "application/vnd.npm.install-v1+json")
            .send()
            .await
            .context("Failed to fetch package metadata")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Package '{}' not found (HTTP {})", name, response.status()));
        }

        let data = response.bytes().await?;
        let registry_data: RegistryResponse = serde_json::from_slice(&data)
            .context("Failed to parse registry response")?;

        // Cache the result
        self.cache.set(&cache_key, data.to_vec()).await?;

        Ok(registry_data)
    }

    pub async fn fetch_package_metadata(&self, name: &str, version: &str) -> Result<Package> {
        let registry_data = self.fetch_registry_data(name).await?;
        
        registry_data.versions.get(version)
            .cloned()
            .ok_or_else(|| anyhow::anyhow!("Version {} not found for package {}", version, name))
    }

    fn deduplicate_and_sort(&self, mut packages: Vec<ResolvedPackage>) -> Result<Vec<ResolvedPackage>> {
        let mut seen = HashSet::new();
        let mut deduplicated = Vec::new();
        
        // Remove exact duplicates
        for package in packages {
            let key = format!("{}@{}", package.name, package.version);
            if !seen.contains(&key) {
                seen.insert(key);
                deduplicated.push(package);
            }
        }
        
        // Sort by dependency order (dependencies first)
        deduplicated.sort_by(|a, b| {
            // Packages with fewer dependencies should come first
            a.dependencies.len().cmp(&b.dependencies.len())
        });
        
        Ok(deduplicated)
    }

    pub async fn get_package_versions(&self, name: &str) -> Result<Vec<String>> {
        let registry_data = self.fetch_registry_data(name).await?;
        let mut versions: Vec<String> = registry_data.versions.keys().cloned().collect();
        
        // Sort versions semantically
        versions.sort_by(|a, b| {
            match (Version::parse(a), Version::parse(b)) {
                (Ok(va), Ok(vb)) => vb.cmp(&va), // Reverse order (newest first)
                _ => b.cmp(a), // Fallback to string comparison
            }
        });
        
        Ok(versions)
    }

    pub async fn get_package_tags(&self, name: &str) -> Result<HashMap<String, String>> {
        let registry_data = self.fetch_registry_data(name).await?;
        Ok(registry_data.dist_tags)
    }
}
