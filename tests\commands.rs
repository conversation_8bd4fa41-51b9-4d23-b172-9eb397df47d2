use std::process::Command;
use tempfile::TempDir;

#[tokio::test]
async fn test_init_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "init", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Initialize a new project"));
}

#[tokio::test]
async fn test_search_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "search", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Search for packages"));
}

#[tokio::test]
async fn test_publish_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "publish", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Publish package to registry"));
}

#[tokio::test]
async fn test_link_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "link", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Link package for development"));
}

#[tokio::test]
async fn test_doctor_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "doctor", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Run diagnostics"));
}
