use crate::ui::UI;
use anyhow::{Context, Result};
use std::collections::{HashMap, HashSet};
use std::env;
use std::path::{Path, PathBuf};
use tokio::fs;
use walkdir::WalkDir;

pub async fn run(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let node_modules_dir = current_dir.join("node_modules");
    
    if !node_modules_dir.exists() {
        ui.warning("No node_modules directory found");
        return Ok();
    }

    let spinner = ui.create_spinner("🔄 Analyzing dependency tree for duplicates...");
    
    let duplicates = find_duplicate_packages(&node_modules_dir).await?;
    
    if duplicates.is_empty() {
        spinner.finish_with_message("🔄 No duplicates found");
        ui.success("Dependencies are already optimized!");
        return Ok();
    }
    
    spinner.finish_with_message(&format!("🔄 Found {} duplicate packages", duplicates.len()));
    
    let dedupe_progress = ui.create_install_progress(duplicates.len());
    let mut removed_count = 0;
    let mut saved_space = 0u64;
    
    for (package_name, duplicate_paths) in duplicates {
        dedupe_progress.set_message(format!("Deduplicating {}", package_name));
        
        // Keep the version in the root node_modules, remove nested duplicates
        let mut paths_to_remove = duplicate_paths;
        paths_to_remove.sort_by_key(|p| p.components().count());
        paths_to_remove.remove(0); // Keep the first (shortest path)
        
        for path in paths_to_remove {
            if let Ok(size) = calculate_directory_size(&path).await {
                saved_space += size;
            }
            
            if let Err(e) = fs::remove_dir_all(&path).await {
                ui.warning(&format!("Failed to remove duplicate {}: {}", path.display(), e));
            } else {
                removed_count += 1;
            }
        }
        
        dedupe_progress.inc(1);
    }
    
    dedupe_progress.finish_with_message("🔄 Deduplication complete");
    
    ui.success(&format!(
        "Removed {} duplicate packages, saved {}",
        removed_count,
        crate::utils::format_bytes(saved_space)
    ));
    
    Ok(())
}

async fn find_duplicate_packages(node_modules_dir: &Path) -> Result<HashMap<String, Vec<PathBuf>>> {
    let mut package_versions: HashMap<String, HashMap<String, Vec<PathBuf>>> = HashMap::new();
    
    // Walk through all node_modules directories
    for entry in WalkDir::new(node_modules_dir)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        
        // Look for package.json files
        if path.file_name() == Some(std::ffi::OsStr::new("package.json")) {
            let package_dir = path.parent().unwrap();
            
            // Skip if this is the root package.json
            if package_dir == node_modules_dir.parent().unwrap() {
                continue;
            }
            
            if let Ok(content) = fs::read_to_string(path).await {
                if let Ok(package_json) = serde_json::from_str::<serde_json::Value>(&content) {
                    if let (Some(name), Some(version)) = (
                        package_json.get("name").and_then(|n| n.as_str()),
                        package_json.get("version").and_then(|v| v.as_str())
                    ) {
                        package_versions
                            .entry(name.to_string())
                            .or_insert_with(HashMap::new)
                            .entry(version.to_string())
                            .or_insert_with(Vec::new)
                            .push(package_dir.to_path_buf());
                    }
                }
            }
        }
    }
    
    // Find packages with multiple instances of the same version
    let mut duplicates = HashMap::new();
    
    for (package_name, versions) in package_versions {
        for (version, paths) in versions {
            if paths.len() > 1 {
                duplicates.insert(
                    format!("{}@{}", package_name, version),
                    paths
                );
            }
        }
    }
    
    Ok(duplicates)
}

async fn calculate_directory_size(dir: &Path) -> Result<u64> {
    let mut total_size = 0u64;
    
    for entry in WalkDir::new(dir).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Ok(metadata) = entry.metadata() {
                total_size += metadata.len();
            }
        }
    }
    
    Ok(total_size)
}
