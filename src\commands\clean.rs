use crate::ui::UI;
use anyhow::{Context, Result};
use std::env;
use std::path::Path;
use tokio::fs;
use walkdir::WalkDir;

pub async fn run(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    ui.info("🧹 Cleaning project...");
    
    let mut total_removed = 0u64;
    let mut total_size_freed = 0u64;
    
    // Clean node_modules
    let node_modules_size = clean_node_modules(&current_dir, ui).await?;
    if node_modules_size > 0 {
        total_removed += 1;
        total_size_freed += node_modules_size;
    }
    
    // Clean build artifacts
    let build_size = clean_build_artifacts(&current_dir, ui).await?;
    total_size_freed += build_size;
    
    // Clean cache files
    let cache_size = clean_cache_files(&current_dir, ui).await?;
    total_size_freed += cache_size;
    
    // Clean log files
    let log_size = clean_log_files(&current_dir, ui).await?;
    total_size_freed += log_size;
    
    // Clean temporary files
    let temp_size = clean_temp_files(&current_dir, ui).await?;
    total_size_freed += temp_size;
    
    if total_size_freed > 0 {
        ui.success(&format!(
            "✅ Cleaned project - freed {}",
            crate::utils::format_bytes(total_size_freed)
        ));
    } else {
        ui.info("Project is already clean");
    }
    
    Ok(())
}

async fn clean_node_modules(project_dir: &Path, ui: &UI) -> Result<u64> {
    let node_modules_dir = project_dir.join("node_modules");
    
    if !node_modules_dir.exists() {
        return Ok(0);
    }
    
    let size = calculate_directory_size(&node_modules_dir).await?;
    
    ui.info("Removing node_modules...");
    fs::remove_dir_all(&node_modules_dir).await
        .context("Failed to remove node_modules")?;
    
    ui.success(&format!("✅ Removed node_modules ({})", crate::utils::format_bytes(size)));
    
    Ok(size)
}

async fn clean_build_artifacts(project_dir: &Path, ui: &UI) -> Result<u64> {
    let build_dirs = vec![
        "dist",
        "build",
        "out",
        "lib",
        ".next",
        ".nuxt",
        "coverage",
        ".nyc_output",
    ];
    
    let mut total_size = 0u64;
    
    for dir_name in build_dirs {
        let dir_path = project_dir.join(dir_name);
        if dir_path.exists() && dir_path.is_dir() {
            let size = calculate_directory_size(&dir_path).await?;
            if size > 0 {
                fs::remove_dir_all(&dir_path).await?;
                ui.info(&format!("Removed {} ({})", dir_name, crate::utils::format_bytes(size)));
                total_size += size;
            }
        }
    }
    
    Ok(total_size)
}

async fn clean_cache_files(project_dir: &Path, ui: &UI) -> Result<u64> {
    let cache_patterns = vec![
        ".cache",
        ".parcel-cache",
        ".webpack-cache",
        ".eslintcache",
        ".stylelintcache",
        "tsconfig.tsbuildinfo",
    ];
    
    let mut total_size = 0u64;
    
    for pattern in cache_patterns {
        let cache_path = project_dir.join(pattern);
        if cache_path.exists() {
            let size = if cache_path.is_dir() {
                calculate_directory_size(&cache_path).await?
            } else {
                cache_path.metadata().await?.len()
            };
            
            if size > 0 {
                if cache_path.is_dir() {
                    fs::remove_dir_all(&cache_path).await?;
                } else {
                    fs::remove_file(&cache_path).await?;
                }
                ui.info(&format!("Removed {} ({})", pattern, crate::utils::format_bytes(size)));
                total_size += size;
            }
        }
    }
    
    Ok(total_size)
}

async fn clean_log_files(project_dir: &Path, ui: &UI) -> Result<u64> {
    let mut total_size = 0u64;
    
    // Find and remove log files
    for entry in WalkDir::new(project_dir)
        .max_depth(2)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        if let Some(extension) = path.extension() {
            if extension == "log" {
                if let Ok(metadata) = path.metadata() {
                    let size = metadata.len();
                    fs::remove_file(path).await?;
                    ui.info(&format!("Removed {} ({})", 
                        path.file_name().unwrap().to_string_lossy(),
                        crate::utils::format_bytes(size)
                    ));
                    total_size += size;
                }
            }
        }
    }
    
    // Remove npm debug logs
    let npm_debug_log = project_dir.join("npm-debug.log");
    if npm_debug_log.exists() {
        let size = npm_debug_log.metadata().await?.len();
        fs::remove_file(&npm_debug_log).await?;
        ui.info(&format!("Removed npm-debug.log ({})", crate::utils::format_bytes(size)));
        total_size += size;
    }
    
    Ok(total_size)
}

async fn clean_temp_files(project_dir: &Path, ui: &UI) -> Result<u64> {
    let temp_patterns = vec![
        ".tmp",
        ".temp",
        "*.tmp",
        "*.temp",
        ".DS_Store",
        "Thumbs.db",
    ];
    
    let mut total_size = 0u64;
    
    for entry in WalkDir::new(project_dir)
        .max_depth(3)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("");
        
        let should_remove = temp_patterns.iter().any(|pattern| {
            if pattern.contains('*') {
                let pattern_without_star = pattern.replace("*", "");
                file_name.ends_with(&pattern_without_star)
            } else {
                file_name == *pattern
            }
        });
        
        if should_remove {
            if let Ok(metadata) = path.metadata() {
                let size = metadata.len();
                if path.is_dir() {
                    fs::remove_dir_all(path).await?;
                } else {
                    fs::remove_file(path).await?;
                }
                ui.info(&format!("Removed {} ({})", file_name, crate::utils::format_bytes(size)));
                total_size += size;
            }
        }
    }
    
    Ok(total_size)
}

async fn calculate_directory_size(dir: &Path) -> Result<u64> {
    let mut total_size = 0u64;
    
    for entry in WalkDir::new(dir).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Ok(metadata) = entry.metadata() {
                total_size += metadata.len();
            }
        }
    }
    
    Ok(total_size)
}
