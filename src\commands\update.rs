use crate::installer::Installer;
use crate::resolver::Resolver;
use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;
use std::time::Instant;
use tokio::fs;

pub async fn run(packages: Vec<String>, ui: &UI) -> Result<()> {
    let start_time = Instant::now();
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found in current directory"));
    }

    let content = fs::read_to_string(&package_json_path).await
        .context("Failed to read package.json")?;
    
    let package_json: PackageJson = serde_json::from_str(&content)
        .context("Failed to parse package.json")?;

    let mut packages_to_update = Vec::new();
    
    if packages.is_empty() {
        // Update all packages
        ui.info("🔄 Updating all packages...");
        
        if let Some(deps) = &package_json.dependencies {
            for (name, _) in deps {
                packages_to_update.push((name.clone(), "latest".to_string()));
            }
        }
        
        if let Some(dev_deps) = &package_json.dev_dependencies {
            for (name, _) in dev_deps {
                packages_to_update.push((name.clone(), "latest".to_string()));
            }
        }
    } else {
        // Update specific packages
        ui.info(&format!("🔄 Updating {} packages...", packages.len()));
        
        for package in packages {
            packages_to_update.push((package, "latest".to_string()));
        }
    }

    if packages_to_update.is_empty() {
        ui.info("No packages to update");
        return Ok();
    }

    let spinner = ui.create_spinner(&format!("🔍 Resolving {} package updates...", packages_to_update.len()));
    
    let resolver = Resolver::new()?;
    let resolved = resolver.resolve_dependencies(&packages_to_update).await?;
    
    spinner.finish_with_message(&format!("🔗 Resolved {} updates", resolved.len()));

    let installer = Installer::new(&current_dir)?;
    installer.install_packages(&resolved, ui).await?;

    // Update package.json with new versions
    update_package_json_versions(&current_dir, &resolved).await?;

    let duration = start_time.elapsed();
    ui.success(&format!("Updated {} packages in {:.1}s!", resolved.len(), duration.as_secs_f64()));

    Ok(())
}

async fn update_package_json_versions(project_dir: &std::path::Path, packages: &[crate::types::ResolvedPackage]) -> Result<()> {
    let package_json_path = project_dir.join("package.json");
    let content = fs::read_to_string(&package_json_path).await?;
    let mut package_json: PackageJson = serde_json::from_str(&content)?;

    for package in packages {
        // Update in dependencies
        if let Some(deps) = &mut package_json.dependencies {
            if deps.contains_key(&package.name) {
                deps.insert(package.name.clone(), format!("^{}", package.version));
            }
        }
        
        // Update in dev_dependencies
        if let Some(dev_deps) = &mut package_json.dev_dependencies {
            if dev_deps.contains_key(&package.name) {
                dev_deps.insert(package.name.clone(), format!("^{}", package.version));
            }
        }
    }

    let updated_content = serde_json::to_string_pretty(&package_json)?;
    fs::write(&package_json_path, updated_content).await?;

    Ok(())
}
