[package]
name = "nx"
version = "1.0.0"
edition = "2021"
authors = ["NX Team"]
description = "Ultra-fast package manager for Node.js ecosystems"
license = "MIT"
repository = "https://github.com/nx-cli/nx"

[[bin]]
name = "nx"
path = "src/main.rs"

[dependencies]
clap = { version = "4.4", features = ["derive", "color"] }
tokio = { version = "1.35", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
indicatif = "0.17"
crossterm = "0.27"
colored = "2.1"
rayon = "1.8"
flate2 = "1.0"
tar = "0.4"
blake3 = "1.5"
tempfile = "3.8"
regex = "1.10"
semver = "1.0"
dirs = "5.0"
anyhow = "1.0"
thiserror = "1.0"
futures = "0.3"
uuid = { version = "1.6", features = ["v4"] }
bincode = "1.3"
walkdir = "2.4"
which = "4.4"
urlencoding = "2.1"
chrono = { version = "0.4", features = ["serde"] }

[profile.release]
lto = true
strip = true
codegen-units = 1
panic = "abort"

[dev-dependencies]
tokio-test = "0.4"
