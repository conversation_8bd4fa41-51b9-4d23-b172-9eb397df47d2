use crate::resolver::Resolver;
use crate::ui::UI;
use anyhow::{Context, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize)]
struct SearchResponse {
    objects: Vec<SearchResult>,
    total: u32,
    time: String,
}

#[derive(Debug, Deserialize)]
struct SearchResult {
    package: PackageInfo,
    score: Score,
}

#[derive(Debug, Deserialize)]
struct PackageInfo {
    name: String,
    version: String,
    description: Option<String>,
    keywords: Option<Vec<String>>,
    author: Option<Author>,
    publisher: Option<Publisher>,
    date: String,
}

#[derive(Debug, Deserialize)]
struct Author {
    name: String,
}

#[derive(Debug, Deserialize)]
struct Publisher {
    username: String,
}

#[derive(Debug, Deserialize)]
struct Score {
    #[serde(rename = "final")]
    final_score: f64,
    detail: ScoreDetail,
}

#[derive(Debug, Deserialize)]
struct ScoreDetail {
    quality: f64,
    popularity: f64,
    maintenance: f64,
}

pub async fn run(query: String, limit: Option<usize>, ui: &UI) -> Result<()> {
    let spinner = ui.create_spinner(&format!("🔍 Searching for '{}'...", query));
    
    let client = Client::new();
    let search_url = format!(
        "https://registry.npmjs.org/-/v1/search?text={}&size={}",
        urlencoding::encode(&query),
        limit.unwrap_or(20)
    );
    
    let response = client
        .get(&search_url)
        .send()
        .await
        .context("Failed to search packages")?;
    
    if !response.status().is_success() {
        return Err(anyhow::anyhow!("Search request failed: {}", response.status()));
    }
    
    let search_result: SearchResponse = response
        .json()
        .await
        .context("Failed to parse search response")?;
    
    spinner.finish_with_message(&format!("🔍 Found {} packages", search_result.objects.len()));
    
    if search_result.objects.is_empty() {
        ui.warning(&format!("No packages found for '{}'", query));
        return Ok();
    }
    
    // Display results in a table
    let mut rows = Vec::new();
    for result in &search_result.objects {
        let package = &result.package;
        let score = format!("{:.1}", result.score.final_score * 100.0);
        let description = package.description
            .as_ref()
            .map(|d| truncate_string(d, 50))
            .unwrap_or_else(|| "No description".to_string());
        let author = package.author
            .as_ref()
            .map(|a| a.name.clone())
            .or_else(|| package.publisher.as_ref().map(|p| p.username.clone()))
            .unwrap_or_else(|| "Unknown".to_string());
        
        rows.push(vec![
            package.name.clone(),
            package.version.clone(),
            description,
            author,
            format!("{}%", score),
        ]);
    }
    
    ui.print_table(&["Name", "Version", "Description", "Author", "Score"], &rows);
    
    ui.info(&format!("Found {} packages in {}", search_result.total, search_result.time));
    ui.info("Use 'nx info <package>' for more details or 'nx install <package>' to install");
    
    Ok(())
}

fn truncate_string(s: &str, max_len: usize) -> String {
    if s.len() <= max_len {
        s.to_string()
    } else {
        format!("{}...", &s[..max_len.saturating_sub(3)])
    }
}
