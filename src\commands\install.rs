use crate::installer::Installer;
use crate::resolver::Resolver;
use crate::types::PackageJson;
use crate::ui::UI;
use crate::lockfile::LockFileManager;
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;
use std::path::Path;
use std::time::Instant;
use tokio::fs;

pub async fn run(packages: Vec<String>, global: bool, save_dev: bool, save_exact: bool, ui: &UI) -> Result<()> {
    let start_time = Instant::now();
    
    if packages.is_empty() {
        // Install from package.json
        install_from_package_json(ui).await
    } else {
        // Install specific packages
        install_packages(packages, global, save_dev, save_exact, ui).await
    }?;

    let duration = start_time.elapsed();
    ui.success(&format!("Done in {:.1}s!", duration.as_secs_f64()));
    
    Ok(())
}

pub async fn run_ci(ui: &UI) -> Result<()> {
    let start_time = Instant::now();
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    ui.info("🔒 Installing from lockfile...");
    
    // Try to install from lockfile first
    if let Ok(_) = LockFileManager::install_from_lockfile(&current_dir, ui).await {
        let duration = start_time.elapsed();
        ui.success(&format!("CI install completed in {:.1}s!", duration.as_secs_f64()));
        return Ok(());
    }
    
    // Fallback to regular install if no lockfile
    ui.warning("No lockfile found, falling back to regular install");
    install_from_package_json(ui).await?;
    
    let duration = start_time.elapsed();
    ui.success(&format!("CI install completed in {:.1}s!", duration.as_secs_f64()));
    
    Ok(())
}

async fn install_from_package_json(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found in current directory"));
    }

    let content = fs::read_to_string(&package_json_path).await
        .context("Failed to read package.json")?;
    
    let package_json: PackageJson = serde_json::from_str(&content)
        .context("Failed to parse package.json")?;

    let mut packages_to_install = Vec::new();
    
    // Add dependencies
    if let Some(deps) = &package_json.dependencies {
        for (name, version) in deps {
            packages_to_install.push((name.clone(), version.clone()));
        }
    }
    
    // Add dev dependencies
    if let Some(dev_deps) = &package_json.dev_dependencies {
        for (name, version) in dev_deps {
            packages_to_install.push((name.clone(), version.clone()));
        }
    }

    if packages_to_install.is_empty() {
        ui.info("No dependencies to install");
        return Ok(());
    }

    let spinner = ui.create_spinner(&format!("🔍 Resolving {} dependencies...", packages_to_install.len()));
    
    let resolver = Resolver::new()?;
    let resolved = resolver.resolve_dependencies(&packages_to_install).await?;
    
    spinner.finish_with_message(&format!("🔗 Resolved {} dependencies", resolved.len()));

    let installer = Installer::new(&current_dir)?;
    installer.install_packages(&resolved, ui).await?;

    // Print summary table
    let mut rows = Vec::new();
    for package in &resolved {
        rows.push(vec![
            package.name.clone(),
            package.version.clone(),
            "N/A".to_string(), // Size would need to be calculated
            "N/A".to_string(), // Time would need to be tracked
        ]);
    }

    ui.print_table(&["Package", "Version", "Size", "Time"], &rows);

    Ok(())
}

async fn install_packages(packages: Vec<String>, global: bool, save_dev: bool, save_exact: bool, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    // Parse package specifications
    let mut packages_to_install = Vec::new();
    for pkg_spec in packages {
        let (name, version) = parse_package_spec(&pkg_spec);
        packages_to_install.push((name, version));
    }

    let spinner = ui.create_spinner(&format!("🔍 Resolving {} packages...", packages_to_install.len()));
    
    let resolver = Resolver::new()?;
    let resolved = resolver.resolve_dependencies(&packages_to_install).await?;
    
    spinner.finish_with_message(&format!("🔗 Resolved {} dependencies", resolved.len()));

    let install_dir = if global {
        dirs::home_dir()
            .context("Could not find home directory")?
            .join(".nx")
            .join("global")
    } else {
        current_dir
    };

    let installer = Installer::new(&install_dir)?;
    installer.install_packages(&resolved, ui).await?;

    // Update package.json if not global
    if !global {
        update_package_json(&current_dir, &packages_to_install, save_dev, save_exact).await?;
    }

    Ok(())
}

fn parse_package_spec(spec: &str) -> (String, String) {
    if let Some(at_pos) = spec.rfind('@') {
        if at_pos > 0 { // Make sure @ is not at the beginning (scoped packages)
            let name = spec[..at_pos].to_string();
            let version = spec[at_pos + 1..].to_string();
            return (name, version);
        }
    }
    (spec.to_string(), "latest".to_string())
}

async fn update_package_json(project_dir: &Path, packages: &[(String, String)], save_dev: bool, save_exact: bool) -> Result<()> {
    let package_json_path = project_dir.join("package.json");
    
    let mut package_json = if package_json_path.exists() {
        let content = fs::read_to_string(&package_json_path).await?;
        serde_json::from_str::<PackageJson>(&content)?
    } else {
        PackageJson {
            name: Some("my-project".to_string()),
            version: Some("1.0.0".to_string()),
            dependencies: Some(HashMap::new()),
            dev_dependencies: Some(HashMap::new()),
            peer_dependencies: None,
            optional_dependencies: None,
            scripts: None,
        }
    };

    let deps = if save_dev {
        package_json.dev_dependencies.get_or_insert_with(HashMap::new)
    } else {
        package_json.dependencies.get_or_insert_with(HashMap::new)
    };

    for (name, version) in packages {
        let version_to_save = if save_exact {
            version.clone()
        } else if version == "latest" {
            format!("^{}", version) // This would need actual version resolution
        } else {
            version.clone()
        };
        deps.insert(name.clone(), version_to_save);
    }

    let updated_content = serde_json::to_string_pretty(&package_json)?;
    fs::write(&package_json_path, updated_content).await?;

    Ok(())
}
