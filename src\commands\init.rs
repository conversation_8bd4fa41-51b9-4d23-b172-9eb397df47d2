use crate::ui::UI;
use anyhow::{Context, Result};
use serde_json::json;
use std::env;
use std::io::{self, Write};
use tokio::fs;

pub async fn run(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if package_json_path.exists() {
        ui.warning("package.json already exists");
        print!("Overwrite existing package.json? (y/N): ");
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        
        if !input.trim().to_lowercase().starts_with('y') {
            ui.info("Initialization cancelled");
            return Ok(());
        }
    }
    
    ui.info("🚀 Initializing new Node.js project...");
    
    // Interactive prompts
    let name = prompt_with_default("Package name", &get_default_name(&current_dir))?;
    let version = prompt_with_default("Version", "1.0.0")?;
    let description = prompt_optional("Description")?;
    let entry_point = prompt_with_default("Entry point", "index.js")?;
    let test_command = prompt_optional("Test command")?;
    let git_repository = prompt_optional("Git repository")?;
    let keywords = prompt_optional("Keywords (comma-separated)")?;
    let author = prompt_optional("Author")?;
    let license = prompt_with_default("License", "ISC")?;
    
    // Create package.json
    let mut package_json = json!({
        "name": name,
        "version": version,
        "main": entry_point,
        "license": license
    });
    
    if let Some(desc) = description {
        package_json["description"] = json!(desc);
    }
    
    if let Some(test) = test_command {
        package_json["scripts"] = json!({
            "test": test
        });
    } else {
        package_json["scripts"] = json!({
            "test": "echo \"Error: no test specified\" && exit 1"
        });
    }
    
    if let Some(repo) = git_repository {
        package_json["repository"] = json!({
            "type": "git",
            "url": repo
        });
    }
    
    if let Some(kw) = keywords {
        let keywords_vec: Vec<&str> = kw.split(',').map(|s| s.trim()).collect();
        package_json["keywords"] = json!(keywords_vec);
    }
    
    if let Some(auth) = author {
        package_json["author"] = json!(auth);
    }
    
    // Write package.json
    let content = serde_json::to_string_pretty(&package_json)?;
    fs::write(&package_json_path, content).await
        .context("Failed to write package.json")?;
    
    ui.success("✅ package.json created successfully!");
    
    // Create basic project structure
    create_project_structure(&current_dir, &entry_point, ui).await?;
    
    ui.info("📁 Project structure created:");
    ui.info("  ├── package.json");
    ui.info(&format!("  ├── {}", entry_point));
    ui.info("  ├── README.md");
    ui.info("  └── .gitignore");
    
    ui.success("🎉 Project initialized! Run 'nx install' to get started.");
    
    Ok(())
}

fn prompt_with_default(prompt: &str, default: &str) -> Result<String> {
    print!("{} ({}): ", prompt, default);
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();
    
    if input.is_empty() {
        Ok(default.to_string())
    } else {
        Ok(input.to_string())
    }
}

fn prompt_optional(prompt: &str) -> Result<Option<String>> {
    print!("{}: ", prompt);
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    let input = input.trim();
    
    if input.is_empty() {
        Ok(None)
    } else {
        Ok(Some(input.to_string()))
    }
}

fn get_default_name(current_dir: &std::path::Path) -> String {
    current_dir
        .file_name()
        .and_then(|name| name.to_str())
        .unwrap_or("my-project")
        .to_string()
}

async fn create_project_structure(project_dir: &std::path::Path, entry_point: &str, ui: &UI) -> Result<()> {
    // Create entry point file
    let entry_content = if entry_point.ends_with(".js") {
        "console.log('Hello, World!');\n"
    } else if entry_point.ends_with(".ts") {
        "console.log('Hello, World!');\n\nexport {};\n"
    } else {
        "console.log('Hello, World!');\n"
    };
    
    fs::write(project_dir.join(entry_point), entry_content).await?;
    
    // Create README.md
    let readme_content = format!(
        "# {}\n\nA new Node.js project created with NX.\n\n## Installation\n\n```bash\nnx install\n```\n\n## Usage\n\n```bash\nnode {}\n```\n",
        get_default_name(project_dir),
        entry_point
    );
    
    fs::write(project_dir.join("README.md"), readme_content).await?;
    
    // Create .gitignore
    let gitignore_content = "node_modules/\n*.log\n.env\n.DS_Store\ndist/\nbuild/\n";
    fs::write(project_dir.join(".gitignore"), gitignore_content).await?;
    
    Ok(())
}
