use crate::ui::UI;
use anyhow::{Context, Result};
use std::env;
use std::path::Path;
use tokio::fs;

pub async fn run_from_npm(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    ui.info("🔄 Migrating from npm to nx...");
    
    // Check for package-lock.json
    let package_lock_path = current_dir.join("package-lock.json");
    if package_lock_path.exists() {
        ui.info("📋 Found package-lock.json, converting to nx-lock.json...");
        convert_npm_lockfile(&current_dir).await?;
        ui.success("✅ Converted lockfile");
    }
    
    // Check for .npmrc
    let npmrc_path = current_dir.join(".npmrc");
    if npmrc_path.exists() {
        ui.info("⚙️ Found .npmrc, migrating configuration...");
        migrate_npm_config(&npmrc_path).await?;
        ui.success("✅ Migrated npm configuration");
    }
    
    // Remove npm cache if exists
    if let Ok(npm_cache) = std::env::var("npm_config_cache") {
        ui.info(&format!("🗑️ npm cache found at: {}", npm_cache));
        ui.info("Consider running 'npm cache clean --force' to free up space");
    }
    
    ui.success("🎉 Migration from npm completed!");
    ui.info("You can now use 'nx install' instead of 'npm install'");
    
    Ok(())
}

pub async fn run_from_yarn(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    ui.info("🔄 Migrating from Yarn to nx...");
    
    // Check for yarn.lock
    let yarn_lock_path = current_dir.join("yarn.lock");
    if yarn_lock_path.exists() {
        ui.info("📋 Found yarn.lock, converting to nx-lock.json...");
        convert_yarn_lockfile(&current_dir).await?;
        ui.success("✅ Converted lockfile");
    }
    
    // Check for .yarnrc
    let yarnrc_path = current_dir.join(".yarnrc");
    if yarnrc_path.exists() {
        ui.info("⚙️ Found .yarnrc, migrating configuration...");
        migrate_yarn_config(&yarnrc_path).await?;
        ui.success("✅ Migrated Yarn configuration");
    }
    
    ui.success("🎉 Migration from Yarn completed!");
    ui.info("You can now use 'nx install' instead of 'yarn install'");
    
    Ok(())
}

pub async fn run_from_pnpm(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    ui.info("🔄 Migrating from pnpm to nx...");
    
    // Check for pnpm-lock.yaml
    let pnpm_lock_path = current_dir.join("pnpm-lock.yaml");
    if pnpm_lock_path.exists() {
        ui.info("📋 Found pnpm-lock.yaml, converting to nx-lock.json...");
        convert_pnpm_lockfile(&current_dir).await?;
        ui.success("✅ Converted lockfile");
    }
    
    // Check for .npmrc (pnpm uses .npmrc)
    let npmrc_path = current_dir.join(".npmrc");
    if npmrc_path.exists() {
        ui.info("⚙️ Found .npmrc, migrating configuration...");
        migrate_npm_config(&npmrc_path).await?;
        ui.success("✅ Migrated pnpm configuration");
    }
    
    ui.success("🎉 Migration from pnpm completed!");
    ui.info("You can now use 'nx install' instead of 'pnpm install'");
    
    Ok(())
}

async fn convert_npm_lockfile(project_dir: &Path) -> Result<()> {
    // Use existing lockfile manager
    crate::lockfile::LockFileManager::read_lockfile(project_dir).await?;
    Ok(())
}

async fn convert_yarn_lockfile(project_dir: &Path) -> Result<()> {
    let yarn_lock_path = project_dir.join("yarn.lock");
    let content = fs::read_to_string(&yarn_lock_path).await?;
    
    // Simple yarn.lock parser (basic implementation)
    let mut packages = std::collections::HashMap::new();
    let mut current_package = None;
    let mut current_version = None;
    let mut current_resolved = None;
    let mut current_integrity = None;
    
    for line in content.lines() {
        let line = line.trim();
        
        if line.is_empty() || line.starts_with('#') {
            continue;
        }
        
        if line.ends_with(':') && !line.starts_with(' ') {
            // New package entry
            if let (Some(pkg), Some(ver), Some(res)) = (&current_package, &current_version, &current_resolved) {
                packages.insert(
                    format!("{}@{}", pkg, ver),
                    crate::types::LockFilePackage {
                        resolved: res.clone(),
                        integrity: current_integrity.clone().unwrap_or_default(),
                        dependencies: None,
                    }
                );
            }
            
            let package_spec = line.trim_end_matches(':');
            if let Some(at_pos) = package_spec.rfind('@') {
                current_package = Some(package_spec[..at_pos].to_string());
                current_version = Some(package_spec[at_pos + 1..].to_string());
            }
            current_resolved = None;
            current_integrity = None;
        } else if line.starts_with("resolved ") {
            current_resolved = Some(line.strip_prefix("resolved ").unwrap().trim_matches('"').to_string());
        } else if line.starts_with("integrity ") {
            current_integrity = Some(line.strip_prefix("integrity ").unwrap().to_string());
        }
    }
    
    // Save final package
    if let (Some(pkg), Some(ver), Some(res)) = (current_package, current_version, current_resolved) {
        packages.insert(
            format!("{}@{}", pkg, ver),
            crate::types::LockFilePackage {
                resolved: res,
                integrity: current_integrity.unwrap_or_default(),
                dependencies: None,
            }
        );
    }
    
    let lockfile = crate::types::LockFile {
        version: "1.0".to_string(),
        packages,
    };
    
    let nx_lock_path = project_dir.join("nx-lock.json");
    let content = serde_json::to_string_pretty(&lockfile)?;
    fs::write(&nx_lock_path, content).await?;
    
    Ok(())
}

async fn convert_pnpm_lockfile(project_dir: &Path) -> Result<()> {
    let pnpm_lock_path = project_dir.join("pnpm-lock.yaml");
    let content = fs::read_to_string(&pnpm_lock_path).await?;
    
    // Basic YAML parsing for pnpm-lock.yaml
    // This is a simplified implementation
    let mut packages = std::collections::HashMap::new();
    
    // For now, create an empty lockfile and let nx rebuild it
    let lockfile = crate::types::LockFile {
        version: "1.0".to_string(),
        packages,
    };
    
    let nx_lock_path = project_dir.join("nx-lock.json");
    let content = serde_json::to_string_pretty(&lockfile)?;
    fs::write(&nx_lock_path, content).await?;
    
    Ok(())
}

async fn migrate_npm_config(npmrc_path: &Path) -> Result<()> {
    let content = fs::read_to_string(npmrc_path).await?;
    
    for line in content.lines() {
        let line = line.trim();
        if line.is_empty() || line.starts_with('#') {
            continue;
        }
        
        if let Some(eq_pos) = line.find('=') {
            let key = line[..eq_pos].trim();
            let value = line[eq_pos + 1..].trim();
            
            // Map npm config to nx config
            match key {
                "registry" => {
                    crate::commands::config::run_set("registry".to_string(), value.to_string(), &crate::ui::UI::new()).await?;
                }
                "proxy" => {
                    crate::commands::config::run_set("proxy".to_string(), value.to_string(), &crate::ui::UI::new()).await?;
                }
                "https-proxy" => {
                    crate::commands::config::run_set("https-proxy".to_string(), value.to_string(), &crate::ui::UI::new()).await?;
                }
                _ => {
                    // Store as custom config
                    crate::commands::config::run_set(key.to_string(), value.to_string(), &crate::ui::UI::new()).await?;
                }
            }
        }
    }
    
    Ok(())
}

async fn migrate_yarn_config(yarnrc_path: &Path) -> Result<()> {
    let content = fs::read_to_string(yarnrc_path).await?;
    
    for line in content.lines() {
        let line = line.trim();
        if line.is_empty() || line.starts_with('#') {
            continue;
        }
        
        let parts: Vec<&str> = line.split_whitespace().collect();
        if parts.len() >= 2 {
            let key = parts[0];
            let value = parts[1..].join(" ").trim_matches('"').to_string();
            
            match key {
                "registry" => {
                    crate::commands::config::run_set("registry".to_string(), value, &crate::ui::UI::new()).await?;
                }
                "proxy" => {
                    crate::commands::config::run_set("proxy".to_string(), value, &crate::ui::UI::new()).await?;
                }
                "https-proxy" => {
                    crate::commands::config::run_set("https-proxy".to_string(), value, &crate::ui::UI::new()).await?;
                }
                _ => {
                    crate::commands::config::run_set(key.to_string(), value, &crate::ui::UI::new()).await?;
                }
            }
        }
    }
    
    Ok(())
}
