use clap::Parser;
use std::process;

mod cli;
mod resolver;
mod installer;
mod cache;
mod ui;
mod commands;
mod lockfile;
mod types;
mod utils;

use cli::{Cli, Commands, ConfigAction, WorkspaceAction, RegistryAction, SecurityAction, EnvAction, ScriptAction, MigrateAction};
use ui::UI;

#[tokio::main]
async fn main() {
    let cli = Cli::parse();
    let ui = UI::new();

    let result = match cli.command {
        Commands::Install { packages, global, save_dev, save_exact } => {
            commands::install::run(packages, global, save_dev, save_exact, &ui).await
        }
        Commands::Uninstall { packages, global } => {
            commands::uninstall::run(packages, global, &ui).await
        }
        Commands::Update { packages } => {
            commands::update::run(packages, &ui).await
        }
        Commands::Ci => {
            commands::install::run_ci(&ui).await
        }
        Commands::List { global, depth } => {
            commands::list::run(global, depth, &ui).await
        }
        Commands::Outdated => {
            commands::list::run_outdated(&ui).await
        }
        Commands::Info { package } => {
            commands::info::run(package, &ui).await
        }
        Commands::Audit { fix } => {
            commands::audit::run(fix, &ui).await
        }
        Commands::Run { script, args } => {
            commands::run::run_script(script, args, &ui).await
        }
        Commands::Exec { command, args } => {
            commands::run::run_exec(command, args, &ui).await
        }
        Commands::Rebuild => {
            commands::rebuild::run(&ui).await
        }
        Commands::Dedupe => {
            commands::dedupe::run(&ui).await
        }
        Commands::Cache { action } => {
            match action.as_str() {
                "clear" => cache::Cache::clear().await,
                "stats" => cache::Cache::stats().await,
                _ => {
                    ui.error("Invalid cache action. Use 'clear' or 'stats'");
                    process::exit(1);
                }
            }
            Ok(())
        }
        Commands::Doctor => {
            commands::doctor::run(&ui).await
        }
        Commands::Benchmark => {
            commands::benchmark::run(&ui).await
        }
        Commands::Init => {
            commands::init::run(&ui).await
        }
        Commands::Search { query, limit } => {
            commands::search::run(query, Some(limit), &ui).await
        }
        Commands::Publish { tag, dry_run } => {
            commands::publish::run(tag, dry_run, &ui).await
        }
        Commands::Link { package_path } => {
            commands::link::run(package_path, &ui).await
        }
        Commands::Unlink { package_name } => {
            commands::link::run_unlink(package_name, &ui).await
        }
        Commands::ListLinks => {
            commands::link::list_links(&ui).await
        }
        Commands::Config { action, key, value } => {
            match action {
                Some(ConfigAction::Get) => {
                    commands::config::run_get(key, &ui).await
                }
                Some(ConfigAction::Set) => {
                    if let (Some(k), Some(v)) = (key, value) {
                        commands::config::run_set(k, v, &ui).await
                    } else {
                        ui.error("Both key and value are required for set");
                        process::exit(1);
                    }
                }
                Some(ConfigAction::Delete) => {
                    if let Some(k) = key {
                        commands::config::run_delete(k, &ui).await
                    } else {
                        ui.error("Key is required for delete");
                        process::exit(1);
                    }
                }
                Some(ConfigAction::List) | None => {
                    commands::config::run_list(&ui).await
                }
            }
        }
        Commands::Workspace { action } => {
            match action {
                WorkspaceAction::List => {
                    commands::workspace::run_list(&ui).await
                }
                WorkspaceAction::Run { script, workspace } => {
                    commands::workspace::run_run(script, workspace, &ui).await
                }
                WorkspaceAction::Install { workspace } => {
                    commands::workspace::run_install(workspace, &ui).await
                }
            }
        }
        Commands::Version { version_type } => {
            commands::version::run(version_type, &ui).await
        }
        Commands::Clean => {
            commands::clean::run(&ui).await
        }
        Commands::Registry { action } => {
            match action {
                RegistryAction::Login { registry } => {
                    commands::registry::run_login(registry, &ui).await
                }
                RegistryAction::Logout { registry } => {
                    commands::registry::run_logout(registry, &ui).await
                }
                RegistryAction::Whoami { registry } => {
                    commands::registry::run_whoami(registry, &ui).await
                }
                RegistryAction::Add { name, url } => {
                    commands::registry::run_add_registry(name, url, &ui).await
                }
                RegistryAction::Remove { name } => {
                    commands::registry::run_remove_registry(name, &ui).await
                }
                RegistryAction::List => {
                    commands::registry::run_list_registries(&ui).await
                }
            }
        }
        Commands::Security { action } => {
            match action {
                SecurityAction::Scan => {
                    commands::security::run_scan(&ui).await
                }
                SecurityAction::Check { package } => {
                    commands::security::run_check_package(package, &ui).await
                }
                SecurityAction::UpdateDb => {
                    commands::security::run_update_db(&ui).await
                }
            }
        }
        Commands::Env { action } => {
            match action {
                EnvAction::List => {
                    commands::env::run_list(&ui).await
                }
                EnvAction::Set { key, value } => {
                    commands::env::run_set(key, value, &ui).await
                }
                EnvAction::Get { key } => {
                    commands::env::run_get(key, &ui).await
                }
                EnvAction::Unset { key } => {
                    commands::env::run_unset(key, &ui).await
                }
                EnvAction::Validate => {
                    commands::env::run_validate(&ui).await
                }
                EnvAction::Example => {
                    commands::env::run_generate_example(&ui).await
                }
            }
        }
        Commands::Scripts { action } => {
            match action {
                ScriptAction::List => {
                    commands::scripts::run_list(&ui).await
                }
                ScriptAction::Create { name, command } => {
                    commands::scripts::run_create(name, command, &ui).await
                }
                ScriptAction::Remove { name } => {
                    commands::scripts::run_remove(name, &ui).await
                }
                ScriptAction::Rename { old_name, new_name } => {
                    commands::scripts::run_rename(old_name, new_name, &ui).await
                }
                ScriptAction::Validate => {
                    commands::scripts::run_validate(&ui).await
                }
                ScriptAction::Template { template, name } => {
                    commands::scripts::run_template(template, name, &ui).await
                }
            }
        }
        Commands::Migrate { action } => {
            match action {
                MigrateAction::FromNpm => {
                    commands::migrate::run_from_npm(&ui).await
                }
                MigrateAction::FromYarn => {
                    commands::migrate::run_from_yarn(&ui).await
                }
                MigrateAction::FromPnpm => {
                    commands::migrate::run_from_pnpm(&ui).await
                }
            }
        }
    };

    if let Err(e) = result {
        ui.error(&format!("Error: {}", e));
        process::exit(1);
    }
}
