use crate::ui::UI;
use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::PathBuf;
use tokio::fs;

#[derive(Debug, Serialize, Deserialize, Default)]
pub struct Config {
    pub registry: Option<String>,
    pub cache_dir: Option<String>,
    pub global_dir: Option<String>,
    pub proxy: Option<String>,
    pub https_proxy: Option<String>,
    pub no_proxy: Option<String>,
    pub user_agent: Option<String>,
    pub timeout: Option<u64>,
    pub retries: Option<u32>,
    pub custom: HashMap<String, String>,
}

impl Config {
    pub async fn load() -> Result<Self> {
        let config_path = get_config_path()?;
        
        if !config_path.exists() {
            return Ok(Self::default());
        }
        
        let content = fs::read_to_string(&config_path).await
            .context("Failed to read config file")?;
        
        let config: Config = serde_json::from_str(&content)
            .context("Failed to parse config file")?;
        
        Ok(config)
    }
    
    pub async fn save(&self) -> Result<()> {
        let config_path = get_config_path()?;
        
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent).await
                .context("Failed to create config directory")?;
        }
        
        let content = serde_json::to_string_pretty(self)
            .context("Failed to serialize config")?;
        
        fs::write(&config_path, content).await
            .context("Failed to write config file")?;
        
        Ok(())
    }
}

pub async fn run_get(key: Option<String>, ui: &UI) -> Result<()> {
    let config = Config::load().await?;
    
    match key {
        Some(key) => {
            let value = get_config_value(&config, &key);
            match value {
                Some(val) => println!("{}", val),
                None => ui.warning(&format!("Config key '{}' not found", key)),
            }
        }
        None => {
            // Show all config
            display_config(&config, ui);
        }
    }
    
    Ok(())
}

pub async fn run_set(key: String, value: String, ui: &UI) -> Result<()> {
    let mut config = Config::load().await?;
    
    set_config_value(&mut config, &key, &value)?;
    config.save().await?;
    
    ui.success(&format!("Set {}={}", key, value));
    
    Ok(())
}

pub async fn run_delete(key: String, ui: &UI) -> Result<()> {
    let mut config = Config::load().await?;
    
    if delete_config_value(&mut config, &key) {
        config.save().await?;
        ui.success(&format!("Deleted config key: {}", key));
    } else {
        ui.warning(&format!("Config key '{}' not found", key));
    }
    
    Ok(())
}

pub async fn run_list(ui: &UI) -> Result<()> {
    let config = Config::load().await?;
    display_config(&config, ui);
    Ok(())
}

fn get_config_path() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .context("Could not find home directory")?;
    
    Ok(home_dir.join(".nx").join("config.json"))
}

fn get_config_value(config: &Config, key: &str) -> Option<String> {
    match key {
        "registry" => config.registry.clone(),
        "cache-dir" => config.cache_dir.clone(),
        "global-dir" => config.global_dir.clone(),
        "proxy" => config.proxy.clone(),
        "https-proxy" => config.https_proxy.clone(),
        "no-proxy" => config.no_proxy.clone(),
        "user-agent" => config.user_agent.clone(),
        "timeout" => config.timeout.map(|t| t.to_string()),
        "retries" => config.retries.map(|r| r.to_string()),
        _ => config.custom.get(key).cloned(),
    }
}

fn set_config_value(config: &mut Config, key: &str, value: &str) -> Result<()> {
    match key {
        "registry" => config.registry = Some(value.to_string()),
        "cache-dir" => config.cache_dir = Some(value.to_string()),
        "global-dir" => config.global_dir = Some(value.to_string()),
        "proxy" => config.proxy = Some(value.to_string()),
        "https-proxy" => config.https_proxy = Some(value.to_string()),
        "no-proxy" => config.no_proxy = Some(value.to_string()),
        "user-agent" => config.user_agent = Some(value.to_string()),
        "timeout" => {
            let timeout: u64 = value.parse()
                .context("Invalid timeout value")?;
            config.timeout = Some(timeout);
        }
        "retries" => {
            let retries: u32 = value.parse()
                .context("Invalid retries value")?;
            config.retries = Some(retries);
        }
        _ => {
            config.custom.insert(key.to_string(), value.to_string());
        }
    }
    
    Ok(())
}

fn delete_config_value(config: &mut Config, key: &str) -> bool {
    match key {
        "registry" => { config.registry = None; true }
        "cache-dir" => { config.cache_dir = None; true }
        "global-dir" => { config.global_dir = None; true }
        "proxy" => { config.proxy = None; true }
        "https-proxy" => { config.https_proxy = None; true }
        "no-proxy" => { config.no_proxy = None; true }
        "user-agent" => { config.user_agent = None; true }
        "timeout" => { config.timeout = None; true }
        "retries" => { config.retries = None; true }
        _ => config.custom.remove(key).is_some(),
    }
}

fn display_config(config: &Config, ui: &UI) {
    let mut rows = Vec::new();
    
    if let Some(registry) = &config.registry {
        rows.push(vec!["registry".to_string(), registry.clone()]);
    }
    
    if let Some(cache_dir) = &config.cache_dir {
        rows.push(vec!["cache-dir".to_string(), cache_dir.clone()]);
    }
    
    if let Some(global_dir) = &config.global_dir {
        rows.push(vec!["global-dir".to_string(), global_dir.clone()]);
    }
    
    if let Some(proxy) = &config.proxy {
        rows.push(vec!["proxy".to_string(), proxy.clone()]);
    }
    
    if let Some(https_proxy) = &config.https_proxy {
        rows.push(vec!["https-proxy".to_string(), https_proxy.clone()]);
    }
    
    if let Some(no_proxy) = &config.no_proxy {
        rows.push(vec!["no-proxy".to_string(), no_proxy.clone()]);
    }
    
    if let Some(user_agent) = &config.user_agent {
        rows.push(vec!["user-agent".to_string(), user_agent.clone()]);
    }
    
    if let Some(timeout) = &config.timeout {
        rows.push(vec!["timeout".to_string(), timeout.to_string()]);
    }
    
    if let Some(retries) = &config.retries {
        rows.push(vec!["retries".to_string(), retries.to_string()]);
    }
    
    for (key, value) in &config.custom {
        rows.push(vec![key.clone(), value.clone()]);
    }
    
    if rows.is_empty() {
        ui.info("No configuration set");
    } else {
        ui.print_table(&["Key", "Value"], &rows);
    }
}
