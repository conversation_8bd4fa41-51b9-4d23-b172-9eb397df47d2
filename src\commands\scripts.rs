use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;
use std::process::Command;
use tokio::fs;

pub async fn run_list(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let scripts = package_json.scripts.unwrap_or_default();
    
    if scripts.is_empty() {
        ui.info("No scripts defined in package.json");
        return Ok(());
    }
    
    let mut rows = Vec::new();
    for (name, command) in &scripts {
        let description = get_script_description(name);
        rows.push(vec![name.clone(), command.clone(), description]);
    }
    
    ui.print_table(&["Script", "Command", "Description"], &rows);
    
    Ok(())
}

pub async fn run_create(name: String, command: String, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let mut package_json: PackageJson = serde_json::from_str(&content)?;
    
    let mut scripts = package_json.scripts.unwrap_or_default();
    
    if scripts.contains_key(&name) {
        ui.warning(&format!("Script '{}' already exists", name));
        if !confirm_overwrite()? {
            ui.info("Script creation cancelled");
            return Ok(());
        }
    }
    
    scripts.insert(name.clone(), command.clone());
    package_json.scripts = Some(scripts);
    
    let updated_content = serde_json::to_string_pretty(&package_json)?;
    fs::write(&package_json_path, updated_content).await?;
    
    ui.success(&format!("✅ Created script '{}': {}", name, command));
    
    Ok(())
}

pub async fn run_remove(name: String, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let mut package_json: PackageJson = serde_json::from_str(&content)?;
    
    let mut scripts = package_json.scripts.unwrap_or_default();
    
    if scripts.remove(&name).is_some() {
        package_json.scripts = Some(scripts);
        
        let updated_content = serde_json::to_string_pretty(&package_json)?;
        fs::write(&package_json_path, updated_content).await?;
        
        ui.success(&format!("✅ Removed script '{}'", name));
    } else {
        ui.warning(&format!("Script '{}' not found", name));
    }
    
    Ok(())
}

pub async fn run_rename(old_name: String, new_name: String, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let mut package_json: PackageJson = serde_json::from_str(&content)?;
    
    let mut scripts = package_json.scripts.unwrap_or_default();
    
    if let Some(command) = scripts.remove(&old_name) {
        if scripts.contains_key(&new_name) {
            ui.warning(&format!("Script '{}' already exists", new_name));
            if !confirm_overwrite()? {
                // Restore the old script
                scripts.insert(old_name, command);
                ui.info("Script rename cancelled");
                return Ok(());
            }
        }
        
        scripts.insert(new_name.clone(), command);
        package_json.scripts = Some(scripts);
        
        let updated_content = serde_json::to_string_pretty(&package_json)?;
        fs::write(&package_json_path, updated_content).await?;
        
        ui.success(&format!("✅ Renamed script '{}' to '{}'", old_name, new_name));
    } else {
        ui.warning(&format!("Script '{}' not found", old_name));
    }
    
    Ok(())
}

pub async fn run_validate(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let scripts = package_json.scripts.unwrap_or_default();
    
    if scripts.is_empty() {
        ui.info("No scripts to validate");
        return Ok(());
    }
    
    ui.info("🔍 Validating scripts...");
    
    let mut issues = Vec::new();
    let mut valid_scripts = 0;
    
    for (name, command) in &scripts {
        let script_issues = validate_script(name, command, &current_dir).await;
        if script_issues.is_empty() {
            valid_scripts += 1;
        } else {
            issues.extend(script_issues);
        }
    }
    
    if issues.is_empty() {
        ui.success(&format!("✅ All {} scripts are valid", valid_scripts));
    } else {
        ui.warning(&format!("⚠️ Found {} issues in scripts:", issues.len()));
        for (i, issue) in issues.iter().enumerate() {
            println!("  {}. {}", i + 1, issue);
        }
    }
    
    Ok(())
}

pub async fn run_template(template_name: String, script_name: String, ui: &UI) -> Result<()> {
    let command = match template_name.as_str() {
        "test" => "jest",
        "build" => "webpack --mode=production",
        "dev" => "webpack serve --mode=development",
        "start" => "node index.js",
        "lint" => "eslint src/",
        "format" => "prettier --write src/",
        "clean" => "rm -rf dist/",
        "deploy" => "npm run build && npm run upload",
        "watch" => "nodemon index.js",
        "typecheck" => "tsc --noEmit",
        _ => {
            ui.error(&format!("Unknown template: {}", template_name));
            ui.info("Available templates: test, build, dev, start, lint, format, clean, deploy, watch, typecheck");
            return Ok(());
        }
    };
    
    run_create(script_name, command.to_string(), ui).await
}

async fn validate_script(name: &str, command: &str, project_dir: &std::path::Path) -> Vec<String> {
    let mut issues = Vec::new();
    
    // Check for empty commands
    if command.trim().is_empty() {
        issues.push(format!("Script '{}' has empty command", name));
        return issues;
    }
    
    // Check for common issues
    if command.contains("rm -rf /") {
        issues.push(format!("Script '{}' contains dangerous rm command", name));
    }
    
    if command.contains("sudo") {
        issues.push(format!("Script '{}' uses sudo - this may be unsafe", name));
    }
    
    // Check if referenced files exist
    if command.contains("node ") {
        let parts: Vec<&str> = command.split_whitespace().collect();
        if let Some(node_index) = parts.iter().position(|&x| x == "node") {
            if let Some(file) = parts.get(node_index + 1) {
                let file_path = project_dir.join(file);
                if !file_path.exists() {
                    issues.push(format!("Script '{}' references non-existent file: {}", name, file));
                }
            }
        }
    }
    
    // Check for missing binaries
    let commands_to_check = vec!["webpack", "jest", "eslint", "prettier", "tsc"];
    for cmd in commands_to_check {
        if command.contains(cmd) {
            if !is_command_available(cmd) && !is_local_binary_available(cmd, project_dir).await {
                issues.push(format!("Script '{}' uses '{}' which is not installed", name, cmd));
            }
        }
    }
    
    issues
}

fn is_command_available(command: &str) -> bool {
    Command::new("which")
        .arg(command)
        .output()
        .map(|output| output.status.success())
        .unwrap_or(false)
}

async fn is_local_binary_available(command: &str, project_dir: &std::path::Path) -> bool {
    let bin_path = project_dir.join("node_modules").join(".bin").join(command);
    bin_path.exists()
}

fn get_script_description(script_name: &str) -> String {
    match script_name {
        "start" => "Start the application",
        "dev" | "develop" => "Start development server",
        "build" => "Build for production",
        "test" => "Run tests",
        "lint" => "Run linter",
        "format" => "Format code",
        "clean" => "Clean build artifacts",
        "deploy" => "Deploy application",
        "watch" => "Watch for changes",
        "serve" => "Serve built application",
        "typecheck" => "Run TypeScript type checking",
        "prebuild" => "Pre-build hook",
        "postbuild" => "Post-build hook",
        "pretest" => "Pre-test hook",
        "posttest" => "Post-test hook",
        _ => "Custom script",
    }.to_string()
}

fn confirm_overwrite() -> Result<bool> {
    use std::io::{self, Write};
    print!("Overwrite existing script? (y/N): ");
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    Ok(input.trim().to_lowercase().starts_with('y'))
}
