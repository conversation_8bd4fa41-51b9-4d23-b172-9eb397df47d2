use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;
use std::path::Path;
use std::process::{Command, Stdio};
use tokio::fs;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::process::Command as AsyncCommand;

pub async fn run_script(script: String, args: Vec<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }

    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let scripts = package_json.scripts.unwrap_or_default();
    
    if let Some(command) = scripts.get(&script) {
        ui.info(&format!("🚀 Running script: {}", script));
        
        // Show the actual command being run
        ui.info(&format!("Command: {}", command));
        
        let exit_code = execute_command_with_output(command, &args, &current_dir, ui).await?;
        
        if exit_code == 0 {
            ui.success("Script completed successfully");
        } else {
            return Err(anyhow::anyhow!("Script failed with exit code: {}", exit_code));
        }
    } else {
        // List available scripts
        if scripts.is_empty() {
            ui.warning("No scripts defined in package.json");
        } else {
            ui.error(&format!("Script '{}' not found", script));
            ui.info("Available scripts:");
            for (name, cmd) in &scripts {
                println!("  {}: {}", name, cmd);
            }
        }
        return Err(anyhow::anyhow!("Script '{}' not found", script));
    }
    
    Ok(())
}

pub async fn run_exec(command: String, args: Vec<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let bin_dir = current_dir.join("node_modules").join(".bin");
    
    ui.info(&format!("⚡ Executing: {} {}", command, args.join(" ")));
    
    // Check if command exists in node_modules/.bin
    let bin_path = bin_dir.join(&command);
    let command_to_run = if bin_path.exists() {
        bin_path.to_string_lossy().to_string()
    } else {
        command.clone()
    };
    
    let exit_code = execute_command_with_output(&command_to_run, &args, &current_dir, ui).await?;
    
    if exit_code == 0 {
        ui.success("Command completed successfully");
    } else {
        return Err(anyhow::anyhow!("Command failed with exit code: {}", exit_code));
    }
    
    Ok(())
}

async fn execute_command_with_output(
    command: &str,
    args: &[String],
    working_dir: &Path,
    ui: &UI,
) -> Result<i32> {
    let mut cmd = if cfg!(target_os = "windows") {
        let mut cmd = AsyncCommand::new("cmd");
        cmd.args(["/C", command]);
        cmd
    } else {
        let mut cmd = AsyncCommand::new("sh");
        cmd.args(["-c", &format!("{} {}", command, args.join(" "))]);
        cmd
    };
    
    cmd.current_dir(working_dir);
    cmd.stdout(Stdio::piped());
    cmd.stderr(Stdio::piped());
    
    // Add node_modules/.bin to PATH
    let bin_dir = working_dir.join("node_modules").join(".bin");
    if let Ok(path) = env::var("PATH") {
        let new_path = if cfg!(target_os = "windows") {
            format!("{};{}", bin_dir.display(), path)
        } else {
            format!("{}:{}", bin_dir.display(), path)
        };
        cmd.env("PATH", new_path);
    }
    
    let mut child = cmd.spawn().context("Failed to spawn command")?;
    
    // Stream stdout
    if let Some(stdout) = child.stdout.take() {
        let reader = BufReader::new(stdout);
        let mut lines = reader.lines();
        
        tokio::spawn(async move {
            while let Ok(Some(line)) = lines.next_line().await {
                println!("{}", line);
            }
        });
    }
    
    // Stream stderr
    if let Some(stderr) = child.stderr.take() {
        let reader = BufReader::new(stderr);
        let mut lines = reader.lines();
        
        tokio::spawn(async move {
            while let Ok(Some(line)) = lines.next_line().await {
                eprintln!("{}", line);
            }
        });
    }
    
    let status = child.wait().await.context("Failed to wait for command")?;
    
    Ok(status.code().unwrap_or(-1))
}

// Helper function to list available scripts
pub async fn list_scripts(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }

    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: PackageJson = serde_json::from_str(&content)?;
    
    let scripts = package_json.scripts.unwrap_or_default();
    
    if scripts.is_empty() {
        ui.info("No scripts defined in package.json");
        return Ok();
    }
    
    let mut rows = Vec::new();
    for (name, command) in &scripts {
        rows.push(vec![name.clone(), command.clone()]);
    }
    
    ui.print_table(&["Script", "Command"], &rows);
    
    Ok(())
}
