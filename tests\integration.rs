use std::process::Command;
use tempfile::TempDir;

#[tokio::test]
async fn test_install_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "install", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Install packages"));
}

#[tokio::test]
async fn test_list_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "list", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("List installed packages"));
}
