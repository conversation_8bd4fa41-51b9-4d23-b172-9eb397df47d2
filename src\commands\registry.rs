use crate::ui::UI;
use anyhow::{Context, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::fs;

#[derive(Debug, Serialize, Deserialize)]
struct RegistryInfo {
    name: String,
    url: String,
    auth_token: Option<String>,
    always_auth: bool,
    ca_file: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct WhoAmIResponse {
    username: String,
}

pub async fn run_login(registry: Option<String>, ui: &UI) -> Result<()> {
    let registry_url = registry.unwrap_or_else(|| "https://registry.npmjs.org".to_string());
    
    ui.info(&format!("🔐 Logging into registry: {}", registry_url));
    
    // Get credentials
    let username = prompt_input("Username: ")?;
    let password = prompt_password("Password: ")?;
    let email = prompt_input("Email: ")?;
    
    // Authenticate with registry
    let client = Client::new();
    let auth_response = client
        .put(&format!("{}/-/user/org.couchdb.user:{}", registry_url, username))
        .json(&serde_json::json!({
            "_id": format!("org.couchdb.user:{}", username),
            "name": username,
            "password": password,
            "email": email,
            "type": "user",
            "roles": [],
            "date": chrono::Utc::now().to_rfc3339()
        }))
        .send()
        .await
        .context("Failed to authenticate with registry")?;
    
    if !auth_response.status().is_success() {
        return Err(anyhow::anyhow!("Authentication failed: {}", auth_response.status()));
    }
    
    let auth_data: serde_json::Value = auth_response.json().await?;
    let token = auth_data.get("token")
        .and_then(|t| t.as_str())
        .ok_or_else(|| anyhow::anyhow!("No token received from registry"))?;
    
    // Store auth token
    store_auth_token(&registry_url, token).await?;
    
    ui.success(&format!("✅ Logged in as {} to {}", username, registry_url));
    
    Ok(())
}

pub async fn run_logout(registry: Option<String>, ui: &UI) -> Result<()> {
    let registry_url = registry.unwrap_or_else(|| "https://registry.npmjs.org".to_string());
    
    // Remove stored auth token
    remove_auth_token(&registry_url).await?;
    
    ui.success(&format!("✅ Logged out from {}", registry_url));
    
    Ok(())
}

pub async fn run_whoami(registry: Option<String>, ui: &UI) -> Result<()> {
    let registry_url = registry.unwrap_or_else(|| "https://registry.npmjs.org".to_string());
    
    let token = get_auth_token(&registry_url).await?
        .ok_or_else(|| anyhow::anyhow!("Not logged in to {}", registry_url))?;
    
    let client = Client::new();
    let response = client
        .get(&format!("{}/-/whoami", registry_url))
        .header("Authorization", format!("Bearer {}", token))
        .send()
        .await
        .context("Failed to get user info")?;
    
    if !response.status().is_success() {
        return Err(anyhow::anyhow!("Failed to get user info: {}", response.status()));
    }
    
    let user_info: WhoAmIResponse = response.json().await?;
    
    ui.success(&format!("Logged in as: {}", user_info.username));
    
    Ok(())
}

pub async fn run_add_registry(name: String, url: String, ui: &UI) -> Result<()> {
    let registry_info = RegistryInfo {
        name: name.clone(),
        url: url.clone(),
        auth_token: None,
        always_auth: false,
        ca_file: None,
    };
    
    store_registry_info(&name, &registry_info).await?;
    
    ui.success(&format!("✅ Added registry '{}' -> {}", name, url));
    
    Ok(())
}

pub async fn run_remove_registry(name: String, ui: &UI) -> Result<()> {
    remove_registry_info(&name).await?;
    
    ui.success(&format!("✅ Removed registry '{}'", name));
    
    Ok(())
}

pub async fn run_list_registries(ui: &UI) -> Result<()> {
    let registries = list_registries().await?;
    
    if registries.is_empty() {
        ui.info("No custom registries configured");
        return Ok(());
    }
    
    let mut rows = Vec::new();
    for (name, info) in registries {
        let auth_status = if info.auth_token.is_some() { "✅" } else { "❌" };
        rows.push(vec![name, info.url, auth_status.to_string()]);
    }
    
    ui.print_table(&["Name", "URL", "Authenticated"], &rows);
    
    Ok(())
}

fn prompt_input(prompt: &str) -> Result<String> {
    use std::io::{self, Write};
    print!("{}", prompt);
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    Ok(input.trim().to_string())
}

fn prompt_password(prompt: &str) -> Result<String> {
    use std::io::{self, Write};
    print!("{}", prompt);
    io::stdout().flush()?;
    
    // In a real implementation, you'd use a library like `rpassword` to hide input
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    Ok(input.trim().to_string())
}

async fn store_auth_token(registry_url: &str, token: &str) -> Result<()> {
    let config_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx");
    
    fs::create_dir_all(&config_dir).await?;
    
    let auth_file = config_dir.join("auth.json");
    
    let mut auth_data: HashMap<String, String> = if auth_file.exists() {
        let content = fs::read_to_string(&auth_file).await?;
        serde_json::from_str(&content).unwrap_or_default()
    } else {
        HashMap::new()
    };
    
    auth_data.insert(registry_url.to_string(), token.to_string());
    
    let content = serde_json::to_string_pretty(&auth_data)?;
    fs::write(&auth_file, content).await?;
    
    Ok(())
}

async fn remove_auth_token(registry_url: &str) -> Result<()> {
    let config_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx");
    
    let auth_file = config_dir.join("auth.json");
    
    if !auth_file.exists() {
        return Ok(());
    }
    
    let content = fs::read_to_string(&auth_file).await?;
    let mut auth_data: HashMap<String, String> = serde_json::from_str(&content)?;
    
    auth_data.remove(registry_url);
    
    let content = serde_json::to_string_pretty(&auth_data)?;
    fs::write(&auth_file, content).await?;
    
    Ok(())
}

async fn get_auth_token(registry_url: &str) -> Result<Option<String>> {
    let config_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx");
    
    let auth_file = config_dir.join("auth.json");
    
    if !auth_file.exists() {
        return Ok(None);
    }
    
    let content = fs::read_to_string(&auth_file).await?;
    let auth_data: HashMap<String, String> = serde_json::from_str(&content)?;
    
    Ok(auth_data.get(registry_url).cloned())
}

async fn store_registry_info(name: &str, info: &RegistryInfo) -> Result<()> {
    let config_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx");
    
    fs::create_dir_all(&config_dir).await?;
    
    let registries_file = config_dir.join("registries.json");
    
    let mut registries: HashMap<String, RegistryInfo> = if registries_file.exists() {
        let content = fs::read_to_string(&registries_file).await?;
        serde_json::from_str(&content).unwrap_or_default()
    } else {
        HashMap::new()
    };
    
    registries.insert(name.to_string(), info.clone());
    
    let content = serde_json::to_string_pretty(&registries)?;
    fs::write(&registries_file, content).await?;
    
    Ok(())
}

async fn remove_registry_info(name: &str) -> Result<()> {
    let config_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx");
    
    let registries_file = config_dir.join("registries.json");
    
    if !registries_file.exists() {
        return Ok(());
    }
    
    let content = fs::read_to_string(&registries_file).await?;
    let mut registries: HashMap<String, RegistryInfo> = serde_json::from_str(&content)?;
    
    registries.remove(name);
    
    let content = serde_json::to_string_pretty(&registries)?;
    fs::write(&registries_file, content).await?;
    
    Ok(())
}

async fn list_registries() -> Result<HashMap<String, RegistryInfo>> {
    let config_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx");
    
    let registries_file = config_dir.join("registries.json");
    
    if !registries_file.exists() {
        return Ok(HashMap::new());
    }
    
    let content = fs::read_to_string(&registries_file).await?;
    let registries: HashMap<String, RegistryInfo> = serde_json::from_str(&content)?;
    
    Ok(registries)
}
