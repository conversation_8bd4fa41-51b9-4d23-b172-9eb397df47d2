use crate::ui::UI;
use anyhow::{Context, Result};
use std::env;
use std::process::Command;
use std::time::{Duration, Instant};
use tokio::fs;
use tempfile::TempDir;

pub async fn run(ui: &UI) -> Result<()> {
    ui.info("🏁 Starting package manager benchmark...");
    
    let test_packages = vec![
        "lodash@4.17.21",
        "express@4.18.2", 
        "axios@1.7.2",
        "react@18.2.0",
        "typescript@5.3.0"
    ];
    
    ui.info(&format!("Testing with {} packages: {}", test_packages.len(), test_packages.join(", ")));
    
    let mut results = Vec::new();
    
    // Test NX
    if let Ok(nx_time) = benchmark_nx(&test_packages, ui).await {
        results.push(("nx".to_string(), nx_time));
        ui.success(&format!("✅ NX completed in {:.2}s", nx_time.as_secs_f64()));
    }
    
    // Test npm (if available)
    if which::which("npm").is_ok() {
        ui.info("Testing npm...");
        if let Ok(npm_time) = benchmark_npm(&test_packages).await {
            results.push(("npm".to_string(), npm_time));
            ui.success(&format!("✅ npm completed in {:.2}s", npm_time.as_secs_f64()));
        }
    }
    
    // Test yarn (if available)
    if which::which("yarn").is_ok() {
        ui.info("Testing yarn...");
        if let Ok(yarn_time) = benchmark_yarn(&test_packages).await {
            results.push(("yarn".to_string(), yarn_time));
            ui.success(&format!("✅ yarn completed in {:.2}s", yarn_time.as_secs_f64()));
        }
    }
    
    // Test pnpm (if available)
    if which::which("pnpm").is_ok() {
        ui.info("Testing pnpm...");
        if let Ok(pnpm_time) = benchmark_pnpm(&test_packages).await {
            results.push(("pnpm".to_string(), pnpm_time));
            ui.success(&format!("✅ pnpm completed in {:.2}s", pnpm_time.as_secs_f64()));
        }
    }
    
    // Test bun (if available)
    if which::which("bun").is_ok() {
        ui.info("Testing bun...");
        if let Ok(bun_time) = benchmark_bun(&test_packages).await {
            results.push(("bun".to_string(), bun_time));
            ui.success(&format!("✅ bun completed in {:.2}s", bun_time.as_secs_f64()));
        }
    }
    
    // Display results
    display_benchmark_results(&results, ui);
    
    Ok(())
}

async fn benchmark_nx(packages: &[&str], ui: &UI) -> Result<Duration> {
    let temp_dir = TempDir::new().context("Failed to create temp directory")?;
    
    // Create a test package.json
    let package_json = serde_json::json!({
        "name": "benchmark-test",
        "version": "1.0.0",
        "dependencies": {}
    });
    
    let package_json_path = temp_dir.path().join("package.json");
    fs::write(&package_json_path, serde_json::to_string_pretty(&package_json)?).await?;
    
    let start = Instant::now();
    
    // Use our own install command
    let current_dir = env::current_dir()?;
    env::set_current_dir(temp_dir.path())?;
    
    let packages_vec: Vec<String> = packages.iter().map(|s| s.to_string()).collect();
    crate::commands::install::run(packages_vec, false, false, false, ui).await?;
    
    env::set_current_dir(current_dir)?;
    
    Ok(start.elapsed())
}

async fn benchmark_npm(packages: &[&str]) -> Result<Duration> {
    let temp_dir = TempDir::new().context("Failed to create temp directory")?;
    
    // Create a test package.json
    let package_json = serde_json::json!({
        "name": "benchmark-test",
        "version": "1.0.0",
        "dependencies": {}
    });
    
    let package_json_path = temp_dir.path().join("package.json");
    tokio::fs::write(&package_json_path, serde_json::to_string_pretty(&package_json)?).await?;
    
    let start = Instant::now();
    
    let mut cmd = Command::new("npm");
    cmd.args(&["install", "--silent"]);
    cmd.args(packages);
    cmd.current_dir(temp_dir.path());
    
    let output = cmd.output().context("Failed to run npm install")?;
    
    if !output.status.success() {
        return Err(anyhow::anyhow!("npm install failed"));
    }
    
    Ok(start.elapsed())
}

async fn benchmark_yarn(packages: &[&str]) -> Result<Duration> {
    let temp_dir = TempDir::new().context("Failed to create temp directory")?;
    
    let package_json = serde_json::json!({
        "name": "benchmark-test",
        "version": "1.0.0",
        "dependencies": {}
    });
    
    let package_json_path = temp_dir.path().join("package.json");
    tokio::fs::write(&package_json_path, serde_json::to_string_pretty(&package_json)?).await?;
    
    let start = Instant::now();
    
    let mut cmd = Command::new("yarn");
    cmd.args(&["add", "--silent"]);
    cmd.args(packages);
    cmd.current_dir(temp_dir.path());
    
    let output = cmd.output().context("Failed to run yarn add")?;
    
    if !output.status.success() {
        return Err(anyhow::anyhow!("yarn add failed"));
    }
    
    Ok(start.elapsed())
}

async fn benchmark_pnpm(packages: &[&str]) -> Result<Duration> {
    let temp_dir = TempDir::new().context("Failed to create temp directory")?;
    
    let package_json = serde_json::json!({
        "name": "benchmark-test",
        "version": "1.0.0",
        "dependencies": {}
    });
    
    let package_json_path = temp_dir.path().join("package.json");
    tokio::fs::write(&package_json_path, serde_json::to_string_pretty(&package_json)?).await?;
    
    let start = Instant::now();
    
    let mut cmd = Command::new("pnpm");
    cmd.args(&["add", "--silent"]);
    cmd.args(packages);
    cmd.current_dir(temp_dir.path());
    
    let output = cmd.output().context("Failed to run pnpm add")?;
    
    if !output.status.success() {
        return Err(anyhow::anyhow!("pnpm add failed"));
    }
    
    Ok(start.elapsed())
}

async fn benchmark_bun(packages: &[&str]) -> Result<Duration> {
    let temp_dir = TempDir::new().context("Failed to create temp directory")?;
    
    let package_json = serde_json::json!({
        "name": "benchmark-test",
        "version": "1.0.0",
        "dependencies": {}
    });
    
    let package_json_path = temp_dir.path().join("package.json");
    tokio::fs::write(&package_json_path, serde_json::to_string_pretty(&package_json)?).await?;
    
    let start = Instant::now();
    
    let mut cmd = Command::new("bun");
    cmd.args(&["add"]);
    cmd.args(packages);
    cmd.current_dir(temp_dir.path());
    
    let output = cmd.output().context("Failed to run bun add")?;
    
    if !output.status.success() {
        return Err(anyhow::anyhow!("bun add failed"));
    }
    
    Ok(start.elapsed())
}

fn display_benchmark_results(results: &[(String, Duration)], ui: &UI) {
    if results.is_empty() {
        ui.warning("No benchmark results to display");
        return;
    }
    
    println!("\n🏆 Benchmark Results");
    println!("===================");
    
    // Sort by time (fastest first)
    let mut sorted_results = results.to_vec();
    sorted_results.sort_by(|a, b| a.1.cmp(&b.1));
    
    let fastest_time = sorted_results[0].1;
    
    let mut rows = Vec::new();
    for (i, (manager, time)) in sorted_results.iter().enumerate() {
        let time_str = format!("{:.2}s", time.as_secs_f64());
        let speedup = if *time == fastest_time {
            "baseline".to_string()
        } else {
            format!("{:.1}x slower", time.as_secs_f64() / fastest_time.as_secs_f64())
        };
        
        let rank = match i {
            0 => "🥇".to_string(),
            1 => "🥈".to_string(), 
            2 => "🥉".to_string(),
            _ => format!("#{}", i + 1),
        };
        
        rows.push(vec![rank, manager.clone(), time_str, speedup]);
    }
    
    ui.print_table(&["Rank", "Package Manager", "Time", "vs Fastest"], &rows);
    
    // Show winner
    let (winner, winner_time) = &sorted_results[0];
    ui.success(&format!("🎉 {} wins with {:.2}s!", winner, winner_time.as_secs_f64()));
}
