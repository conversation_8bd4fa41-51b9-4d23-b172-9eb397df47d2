use crate::ui::UI;
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;
use std::path::Path;
use tokio::fs;

pub async fn run_list(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    // Check for .env files
    let env_files = vec![".env", ".env.local", ".env.development", ".env.production"];
    let mut found_files = Vec::new();
    
    for env_file in env_files {
        let env_path = current_dir.join(env_file);
        if env_path.exists() {
            found_files.push(env_file);
        }
    }
    
    if found_files.is_empty() {
        ui.info("No .env files found");
        return Ok(());
    }
    
    ui.info(&format!("Found {} .env files:", found_files.len()));
    for file in &found_files {
        ui.info(&format!("  📄 {}", file));
    }
    
    // Show environment variables from .env
    let env_vars = load_env_file(&current_dir.join(".env")).await?;
    
    if !env_vars.is_empty() {
        println!("\nEnvironment variables from .env:");
        let mut rows = Vec::new();
        for (key, value) in env_vars {
            let masked_value = if key.to_lowercase().contains("password") 
                || key.to_lowercase().contains("secret") 
                || key.to_lowercase().contains("key") 
                || key.to_lowercase().contains("token") {
                "***".to_string()
            } else {
                value
            };
            rows.push(vec![key, masked_value]);
        }
        ui.print_table(&["Variable", "Value"], &rows);
    }
    
    Ok(())
}

pub async fn run_set(key: String, value: String, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let env_path = current_dir.join(".env");
    
    let mut env_vars = load_env_file(&env_path).await.unwrap_or_default();
    env_vars.insert(key.clone(), value.clone());
    
    save_env_file(&env_path, &env_vars).await?;
    
    ui.success(&format!("✅ Set {}={}", key, if is_sensitive_key(&key) { "***" } else { &value }));
    
    Ok(())
}

pub async fn run_get(key: String, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let env_path = current_dir.join(".env");
    
    let env_vars = load_env_file(&env_path).await?;
    
    if let Some(value) = env_vars.get(&key) {
        if is_sensitive_key(&key) {
            ui.info(&format!("{}=*** (sensitive value hidden)", key));
        } else {
            println!("{}={}", key, value);
        }
    } else {
        ui.warning(&format!("Environment variable '{}' not found", key));
    }
    
    Ok(())
}

pub async fn run_unset(key: String, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let env_path = current_dir.join(".env");
    
    let mut env_vars = load_env_file(&env_path).await?;
    
    if env_vars.remove(&key).is_some() {
        save_env_file(&env_path, &env_vars).await?;
        ui.success(&format!("✅ Removed environment variable '{}'", key));
    } else {
        ui.warning(&format!("Environment variable '{}' not found", key));
    }
    
    Ok(())
}

pub async fn run_validate(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    ui.info("🔍 Validating environment configuration...");
    
    let mut issues = Vec::new();
    
    // Check for .env files
    let env_files = vec![
        (".env", "Main environment file"),
        (".env.example", "Example environment file"),
        (".env.local", "Local overrides"),
        (".env.development", "Development environment"),
        (".env.production", "Production environment"),
    ];
    
    for (file, description) in env_files {
        let env_path = current_dir.join(file);
        if env_path.exists() {
            ui.success(&format!("✅ Found {} ({})", file, description));
            
            // Validate file contents
            if let Ok(env_vars) = load_env_file(&env_path).await {
                validate_env_vars(&env_vars, file, &mut issues);
            }
        } else if file == ".env.example" {
            issues.push(format!("Missing {} - consider creating one for documentation", file));
        }
    }
    
    // Check for sensitive values in version control
    check_gitignore(&current_dir, &mut issues).await;
    
    // Check for common security issues
    check_security_issues(&current_dir, &mut issues).await;
    
    if issues.is_empty() {
        ui.success("✅ Environment configuration looks good!");
    } else {
        ui.warning(&format!("⚠️ Found {} issues:", issues.len()));
        for (i, issue) in issues.iter().enumerate() {
            println!("  {}. {}", i + 1, issue);
        }
    }
    
    Ok(())
}

pub async fn run_generate_example(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let env_path = current_dir.join(".env");
    let example_path = current_dir.join(".env.example");
    
    if !env_path.exists() {
        ui.warning("No .env file found to generate example from");
        return Ok(());
    }
    
    let env_vars = load_env_file(&env_path).await?;
    
    let mut example_content = String::new();
    example_content.push_str("# Environment Variables\n");
    example_content.push_str("# Copy this file to .env and fill in the values\n\n");
    
    for (key, _value) in env_vars {
        if is_sensitive_key(&key) {
            example_content.push_str(&format!("{}=your_{}_here\n", key, key.to_lowercase()));
        } else {
            example_content.push_str(&format!("{}=\n", key));
        }
    }
    
    fs::write(&example_path, example_content).await?;
    
    ui.success("✅ Generated .env.example file");
    
    Ok(())
}

async fn load_env_file(path: &Path) -> Result<HashMap<String, String>> {
    if !path.exists() {
        return Ok(HashMap::new());
    }
    
    let content = fs::read_to_string(path).await?;
    let mut env_vars = HashMap::new();
    
    for line in content.lines() {
        let line = line.trim();
        
        // Skip comments and empty lines
        if line.is_empty() || line.starts_with('#') {
            continue;
        }
        
        // Parse KEY=VALUE
        if let Some(eq_pos) = line.find('=') {
            let key = line[..eq_pos].trim().to_string();
            let value = line[eq_pos + 1..].trim();
            
            // Remove quotes if present
            let value = if (value.starts_with('"') && value.ends_with('"')) ||
                          (value.starts_with('\'') && value.ends_with('\'')) {
                value[1..value.len()-1].to_string()
            } else {
                value.to_string()
            };
            
            env_vars.insert(key, value);
        }
    }
    
    Ok(env_vars)
}

async fn save_env_file(path: &Path, env_vars: &HashMap<String, String>) -> Result<()> {
    let mut content = String::new();
    
    // Sort keys for consistent output
    let mut keys: Vec<_> = env_vars.keys().collect();
    keys.sort();
    
    for key in keys {
        let value = &env_vars[key];
        
        // Quote values that contain spaces or special characters
        if value.contains(' ') || value.contains('\t') || value.contains('\n') {
            content.push_str(&format!("{}=\"{}\"\n", key, value));
        } else {
            content.push_str(&format!("{}={}\n", key, value));
        }
    }
    
    fs::write(path, content).await?;
    
    Ok(())
}

fn is_sensitive_key(key: &str) -> bool {
    let key_lower = key.to_lowercase();
    key_lower.contains("password") ||
    key_lower.contains("secret") ||
    key_lower.contains("key") ||
    key_lower.contains("token") ||
    key_lower.contains("auth") ||
    key_lower.contains("credential")
}

fn validate_env_vars(env_vars: &HashMap<String, String>, file: &str, issues: &mut Vec<String>) {
    for (key, value) in env_vars {
        // Check for empty sensitive values
        if is_sensitive_key(key) && value.is_empty() {
            issues.push(format!("Empty sensitive variable '{}' in {}", key, file));
        }
        
        // Check for placeholder values
        if value.contains("your_") || value.contains("replace_") || value.contains("changeme") {
            issues.push(format!("Placeholder value detected for '{}' in {}", key, file));
        }
        
        // Check for common insecure values
        if is_sensitive_key(key) {
            let insecure_values = vec!["password", "123456", "admin", "test", "secret"];
            if insecure_values.contains(&value.to_lowercase().as_str()) {
                issues.push(format!("Insecure value for '{}' in {}", key, file));
            }
        }
    }
}

async fn check_gitignore(project_dir: &Path, issues: &mut Vec<String>) {
    let gitignore_path = project_dir.join(".gitignore");
    
    if !gitignore_path.exists() {
        issues.push("No .gitignore file found - .env files may be committed to version control".to_string());
        return;
    }
    
    let content = fs::read_to_string(&gitignore_path).await.unwrap_or_default();
    
    let env_patterns = vec![".env", ".env.local", ".env.*.local"];
    let mut missing_patterns = Vec::new();
    
    for pattern in env_patterns {
        if !content.contains(pattern) {
            missing_patterns.push(pattern);
        }
    }
    
    if !missing_patterns.is_empty() {
        issues.push(format!("Missing .env patterns in .gitignore: {}", missing_patterns.join(", ")));
    }
}

async fn check_security_issues(project_dir: &Path, issues: &mut Vec<String>) {
    // Check if .env files are executable
    let env_files = vec![".env", ".env.local", ".env.development", ".env.production"];
    
    for env_file in env_files {
        let env_path = project_dir.join(env_file);
        if env_path.exists() {
            if let Ok(metadata) = fs::metadata(&env_path).await {
                #[cfg(unix)]
                {
                    use std::os::unix::fs::PermissionsExt;
                    let mode = metadata.permissions().mode();
                    if mode & 0o111 != 0 {
                        issues.push(format!("{} is executable - this may be a security risk", env_file));
                    }
                }
            }
        }
    }
    
    // Check for .env files in public directories
    let public_dirs = vec!["public", "static", "assets"];
    for dir in public_dirs {
        let public_dir = project_dir.join(dir);
        if public_dir.exists() {
            let env_in_public = public_dir.join(".env");
            if env_in_public.exists() {
                issues.push(format!(".env file found in {} directory - this is a security risk", dir));
            }
        }
    }
}
