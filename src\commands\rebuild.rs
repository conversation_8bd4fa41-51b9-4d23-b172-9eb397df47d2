use crate::ui::UI;
use anyhow::{Context, Result};
use std::env;
use std::path::Path;
use std::process::Command;
use tokio::fs;
use walkdir::WalkDir;

pub async fn run(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let node_modules_dir = current_dir.join("node_modules");
    
    if !node_modules_dir.exists() {
        ui.warning("No node_modules directory found");
        return Ok();
    }

    let spinner = ui.create_spinner("🔨 Finding native modules to rebuild...");
    
    let native_modules = find_native_modules(&node_modules_dir).await?;
    
    if native_modules.is_empty() {
        spinner.finish_with_message("🔨 No native modules found");
        ui.info("No native modules require rebuilding");
        return Ok();
    }
    
    spinner.finish_with_message(&format!("🔨 Found {} native modules", native_modules.len()));
    
    let rebuild_progress = ui.create_install_progress(native_modules.len());
    
    for module_path in native_modules {
        let module_name = module_path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("unknown");
            
        rebuild_progress.set_message(format!("Rebuilding {}", module_name));
        
        if let Err(e) = rebuild_native_module(&module_path).await {
            ui.warning(&format!("Failed to rebuild {}: {}", module_name, e));
        } else {
            ui.success(&format!("Rebuilt {}", module_name));
        }
        
        rebuild_progress.inc(1);
    }
    
    rebuild_progress.finish_with_message("🔨 Rebuild complete");
    ui.success("Native modules rebuilt successfully!");
    
    Ok(())
}

async fn find_native_modules(node_modules_dir: &Path) -> Result<Vec<std::path::PathBuf>> {
    let mut native_modules = Vec::new();
    
    for entry in WalkDir::new(node_modules_dir)
        .max_depth(2)
        .into_iter()
        .filter_map(|e| e.ok())
    {
        let path = entry.path();
        
        // Look for binding.gyp files (indicates native module)
        if path.join("binding.gyp").exists() {
            native_modules.push(path.to_path_buf());
            continue;
        }
        
        // Look for prebuilt binaries that might need rebuilding
        if path.join("prebuilds").exists() || 
           path.join("build").exists() ||
           path.join("lib").join("binding").exists() {
            native_modules.push(path.to_path_buf());
        }
    }
    
    Ok(native_modules)
}

async fn rebuild_native_module(module_path: &Path) -> Result<()> {
    // Check if node-gyp is available
    let node_gyp_available = which::which("node-gyp").is_ok();
    
    if !node_gyp_available {
        // Try npm rebuild as fallback
        let output = Command::new("npm")
            .args(&["rebuild"])
            .current_dir(module_path)
            .output()
            .context("Failed to run npm rebuild")?;
            
        if !output.status.success() {
            return Err(anyhow::anyhow!("npm rebuild failed: {}", 
                String::from_utf8_lossy(&output.stderr)));
        }
    } else {
        // Use node-gyp directly
        let output = Command::new("node-gyp")
            .args(&["rebuild"])
            .current_dir(module_path)
            .output()
            .context("Failed to run node-gyp rebuild")?;
            
        if !output.status.success() {
            return Err(anyhow::anyhow!("node-gyp rebuild failed: {}", 
                String::from_utf8_lossy(&output.stderr)));
        }
    }
    
    Ok(())
}
