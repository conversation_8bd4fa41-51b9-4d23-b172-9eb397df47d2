use std::process::Command;
use tempfile::TempDir;

#[tokio::test]
async fn test_registry_commands() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "registry", "list", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("List registries"));
}

#[tokio::test]
async fn test_security_commands() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "security", "scan", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Scan for vulnerabilities"));
}

#[tokio::test]
async fn test_env_commands() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "env", "list", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("List environment variables"));
}

#[tokio::test]
async fn test_scripts_commands() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "scripts", "list", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("List scripts"));
}

#[tokio::test]
async fn test_migrate_commands() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "migrate", "from-npm", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Migrate from npm"));
}
