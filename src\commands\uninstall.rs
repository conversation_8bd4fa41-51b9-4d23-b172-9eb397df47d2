use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::env;
use std::path::Path;
use tokio::fs;
use walkdir::WalkDir;

pub async fn run(packages: Vec<String>, global: bool, ui: &UI) -> Result<()> {
    if packages.is_empty() {
        return Err(anyhow::anyhow!("No packages specified for uninstall"));
    }

    let current_dir = env::current_dir().context("Failed to get current directory")?;
    
    let node_modules_dir = if global {
        dirs::home_dir()
            .context("Could not find home directory")?
            .join(".nx")
            .join("global")
            .join("node_modules")
    } else {
        current_dir.join("node_modules")
    };

    let spinner = ui.create_spinner(&format!("🗑️ Uninstalling {} packages...", packages.len()));

    let mut removed_packages = Vec::new();
    let mut not_found_packages = Vec::new();

    for package in &packages {
        let package_dir = node_modules_dir.join(package);
        if package_dir.exists() {
            // Calculate size before removal
            let size = calculate_directory_size(&package_dir).await.unwrap_or(0);
            
            fs::remove_dir_all(&package_dir).await
                .context(&format!("Failed to remove package {}", package))?;
            
            removed_packages.push((package.clone(), size));
            ui.success(&format!("Removed {}", package));
        } else {
            not_found_packages.push(package.clone());
            ui.warning(&format!("Package {} not found", package));
        }
    }

    spinner.finish_with_message("🗑️ Uninstall complete");

    // Update package.json if not global
    if !global {
        update_package_json_remove(&current_dir, &packages).await?;
    }

    // Prune unused dependencies
    if !removed_packages.is_empty() {
        ui.info("🧹 Pruning unused dependencies...");
        prune_unused_dependencies(&node_modules_dir, ui).await?;
    }

    // Show summary
    if !removed_packages.is_empty() {
        let total_size: u64 = removed_packages.iter().map(|(_, size)| size).sum();
        ui.success(&format!(
            "Removed {} packages, freed {}",
            removed_packages.len(),
            crate::utils::format_bytes(total_size)
        ));
    }

    if !not_found_packages.is_empty() {
        ui.warning(&format!("{} packages were not found", not_found_packages.len()));
    }

    Ok(())
}

async fn calculate_directory_size(dir: &Path) -> Result<u64> {
    let mut total_size = 0u64;
    
    for entry in WalkDir::new(dir).into_iter().filter_map(|e| e.ok()) {
        if entry.file_type().is_file() {
            if let Ok(metadata) = entry.metadata() {
                total_size += metadata.len();
            }
        }
    }
    
    Ok(total_size)
}

async fn update_package_json_remove(project_dir: &Path, packages: &[String]) -> Result<()> {
    let package_json_path = project_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Ok(); // No package.json to update
    }

    let content = fs::read_to_string(&package_json_path).await?;
    let mut package_json: PackageJson = serde_json::from_str(&content)?;

    let mut updated = false;

    // Remove from dependencies
    if let Some(deps) = &mut package_json.dependencies {
        for package in packages {
            if deps.remove(package).is_some() {
                updated = true;
            }
        }
    }

    // Remove from dev_dependencies
    if let Some(dev_deps) = &mut package_json.dev_dependencies {
        for package in packages {
            if dev_deps.remove(package).is_some() {
                updated = true;
            }
        }
    }

    if updated {
        let updated_content = serde_json::to_string_pretty(&package_json)?;
        fs::write(&package_json_path, updated_content).await?;
    }

    Ok(())
}

async fn prune_unused_dependencies(node_modules_dir: &Path, ui: &UI) -> Result<()> {
    // This is a simplified pruning - in a real implementation you'd:
    // 1. Read package.json to get required dependencies
    // 2. Build dependency tree of what's actually needed
    // 3. Remove packages not in the tree
    
    let mut pruned_count = 0;
    let mut entries = fs::read_dir(node_modules_dir).await?;
    
    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();
        if path.is_dir() {
            let name = entry.file_name().to_string_lossy();
            
            // Skip .bin and scoped packages for now
            if name.starts_with('.') || name.starts_with('@') {
                continue;
            }
            
            // Check if package has any dependents (simplified check)
            if is_package_unused(&path).await? {
                fs::remove_dir_all(&path).await?;
                pruned_count += 1;
                ui.info(&format!("Pruned unused package: {}", name));
            }
        }
    }
    
    if pruned_count > 0 {
        ui.success(&format!("Pruned {} unused packages", pruned_count));
    }
    
    Ok(())
}

async fn is_package_unused(_package_dir: &Path) -> Result<bool> {
    // Simplified check - in reality you'd check:
    // 1. If package is in package.json dependencies
    // 2. If package is required by other installed packages
    // 3. If package is imported in source code
    
    // For now, return false to avoid accidentally removing needed packages
    Ok(false)
}
