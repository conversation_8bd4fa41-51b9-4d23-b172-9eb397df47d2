use nx::resolver::Resolver;

#[tokio::test]
async fn test_resolve_latest_version() {
    let resolver = Resolver::new().unwrap();
    let version = resolver.resolve_version("lodash", "latest").await;
    assert!(version.is_ok());
    assert!(!version.unwrap().is_empty());
}

#[tokio::test]
async fn test_resolve_specific_version() {
    let resolver = Resolver::new().unwrap();
    let version = resolver.resolve_version("lodash", "4.17.21").await;
    assert!(version.is_ok());
    assert_eq!(version.unwrap(), "4.17.21");
}

#[tokio::test]
async fn test_resolve_semver_range() {
    let resolver = Resolver::new().unwrap();
    let version = resolver.resolve_version("lodash", "^4.17.0").await;
    assert!(version.is_ok());
    let resolved = version.unwrap();
    assert!(resolved.starts_with("4.17."));
}
