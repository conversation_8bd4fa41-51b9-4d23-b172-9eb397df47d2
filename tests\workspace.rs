use std::process::Command;
use tempfile::TempDir;

#[tokio::test]
async fn test_workspace_list() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "workspace", "list", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("List workspaces"));
}

#[tokio::test]
async fn test_version_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "version", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Version management"));
}

#[tokio::test]
async fn test_clean_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "clean", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Clean project artifacts"));
}

#[tokio::test]
async fn test_config_command() {
    let temp_dir = TempDir::new().unwrap();
    let output = Command::new("cargo")
        .args(&["run", "--", "config", "--help"])
        .current_dir(temp_dir.path())
        .output()
        .expect("Failed to execute command");

    assert!(output.status.success());
    assert!(String::from_utf8_lossy(&output.stdout).contains("Configuration management"));
}
