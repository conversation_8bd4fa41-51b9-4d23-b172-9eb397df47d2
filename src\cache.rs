use anyhow::{Context, Result};
use blake3::Hasher;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::fs as async_fs;

#[derive(Debug, Serialize, Deserialize)]
struct CacheEntry {
    data: Vec<u8>,
    timestamp: u64,
    hits: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct CacheStats {
    total_size: u64,
    total_entries: usize,
    hits: u64,
    misses: u64,
}

pub struct Cache {
    cache_dir: PathBuf,
    max_age_days: u64,
    max_size_gb: u64,
}

impl Cache {
    pub fn new() -> Result<Self> {
        let cache_dir = dirs::home_dir()
            .context("Could not find home directory")?
            .join(".nx")
            .join("cache");

        fs::create_dir_all(&cache_dir)
            .context("Failed to create cache directory")?;

        Ok(Self {
            cache_dir,
            max_age_days: 30,
            max_size_gb: 1,
        })
    }

    pub fn hash_key(&self, key: &str) -> String {
        let mut hasher = Hasher::new();
        hasher.update(key.as_bytes());
        hasher.finalize().to_hex().to_string()
    }

    pub async fn get(&self, key: &str) -> Result<Option<Vec<u8>>> {
        let hash = self.hash_key(key);
        let cache_file = self.cache_dir.join(&hash);

        if !cache_file.exists() {
            return Ok(None);
        }

        let data = async_fs::read(&cache_file).await
            .context("Failed to read cache file")?;

        let entry: CacheEntry = serde_json::from_slice(&data)
            .context("Failed to deserialize cache entry")?;

        // Check if entry is expired
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        if now - entry.timestamp > self.max_age_days * 24 * 60 * 60 {
            let _ = async_fs::remove_file(&cache_file).await;
            return Ok(None);
        }

        // Update hit count
        let updated_entry = CacheEntry {
            data: entry.data.clone(),
            timestamp: entry.timestamp,
            hits: entry.hits + 1,
        };

        let serialized = serde_json::to_string(&updated_entry)
            .context("Failed to serialize cache entry")?;

        let _ = async_fs::write(&cache_file, serialized).await;

        Ok(Some(entry.data))
    }

    pub async fn set(&self, key: &str, data: Vec<u8>) -> Result<()> {
        let hash = self.hash_key(key);
        let cache_file = self.cache_dir.join(&hash);

        let entry = CacheEntry {
            data,
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs(),
            hits: 0,
        };

        let serialized = serde_json::to_string(&entry)
            .context("Failed to serialize cache entry")?;

        async_fs::write(&cache_file, serialized).await
            .context("Failed to write cache file")?;

        // Cleanup old entries if needed
        self.cleanup().await?;

        Ok(())
    }

    async fn cleanup(&self) -> Result<()> {
        let mut entries = Vec::new();
        let mut total_size = 0u64;

        let mut dir = async_fs::read_dir(&self.cache_dir).await?;
        while let Some(entry) = dir.next_entry().await? {
            if let Ok(metadata) = entry.metadata().await {
                total_size += metadata.len();
                entries.push((entry.path(), metadata.len(), metadata.modified().unwrap_or(SystemTime::UNIX_EPOCH)));
            }
        }

        // Remove entries if cache is too large
        if total_size > self.max_size_gb * 1024 * 1024 * 1024 {
            entries.sort_by_key(|(_, _, modified)| *modified);
            
            let mut removed_size = 0u64;
            let target_size = (self.max_size_gb * 1024 * 1024 * 1024) / 2; // Remove half

            for (path, size, _) in entries {
                if removed_size >= target_size {
                    break;
                }
                let _ = async_fs::remove_file(&path).await;
                removed_size += size;
            }
        }

        Ok(())
    }

    pub async fn clear() -> Result<()> {
        let cache = Self::new()?;
        if cache.cache_dir.exists() {
            async_fs::remove_dir_all(&cache.cache_dir).await
                .context("Failed to clear cache")?;
            fs::create_dir_all(&cache.cache_dir)
                .context("Failed to recreate cache directory")?;
        }
        println!("✅ Cache cleared");
        Ok(())
    }

    pub async fn stats() -> Result<()> {
        let cache = Self::new()?;
        let mut total_size = 0u64;
        let mut total_entries = 0usize;
        let mut total_hits = 0u64;

        if cache.cache_dir.exists() {
            let mut dir = async_fs::read_dir(&cache.cache_dir).await?;
            while let Some(entry) = dir.next_entry().await? {
                if let Ok(data) = async_fs::read(entry.path()).await {
                    if let Ok(cache_entry) = serde_json::from_slice::<CacheEntry>(&data) {
                        total_size += data.len() as u64;
                        total_entries += 1;
                        total_hits += cache_entry.hits;
                    }
                }
            }
        }

        println!("📊 Cache Statistics");
        println!("Total size: {:.2} MB", total_size as f64 / 1024.0 / 1024.0);
        println!("Total entries: {}", total_entries);
        println!("Total hits: {}", total_hits);
        println!("Cache directory: {}", cache.cache_dir.display());

        Ok(())
    }
}
