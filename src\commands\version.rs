use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use semver::Version;
use std::env;
use std::io::{self, Write};
use tokio::fs;

pub async fn run(version_type: Option<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let content = fs::read_to_string(&package_json_path).await?;
    let mut package_json: PackageJson = serde_json::from_str(&content)?;
    
    let current_version = package_json.version
        .as_ref()
        .ok_or_else(|| anyhow::anyhow!("No version field in package.json"))?;
    
    let current_semver = Version::parse(current_version)
        .context("Invalid version format in package.json")?;
    
    let new_version = match version_type.as_deref() {
        Some("patch") => {
            let mut new_ver = current_semver.clone();
            new_ver.patch += 1;
            new_ver
        }
        Some("minor") => {
            let mut new_ver = current_semver.clone();
            new_ver.minor += 1;
            new_ver.patch = 0;
            new_ver
        }
        Some("major") => {
            let mut new_ver = current_semver.clone();
            new_ver.major += 1;
            new_ver.minor = 0;
            new_ver.patch = 0;
            new_ver
        }
        Some("prerelease") => {
            let mut new_ver = current_semver.clone();
            if new_ver.pre.is_empty() {
                new_ver.patch += 1;
                new_ver.pre = semver::Prerelease::new("0").unwrap();
            } else {
                // Increment prerelease number
                if let Ok(pre_num) = new_ver.pre.as_str().parse::<u64>() {
                    new_ver.pre = semver::Prerelease::new(&(pre_num + 1).to_string()).unwrap();
                } else {
                    new_ver.pre = semver::Prerelease::new("1").unwrap();
                }
            }
            new_ver
        }
        Some(custom_version) => {
            Version::parse(custom_version)
                .context("Invalid version format provided")?
        }
        None => {
            // Interactive version selection
            return interactive_version_bump(&current_semver, &mut package_json, &package_json_path, ui).await;
        }
    };
    
    // Confirm version bump
    ui.info(&format!("Current version: {}", current_version));
    ui.info(&format!("New version: {}", new_version));
    
    if !confirm_version_bump()? {
        ui.info("Version bump cancelled");
        return Ok(());
    }
    
    // Update package.json
    package_json.version = Some(new_version.to_string());
    let updated_content = serde_json::to_string_pretty(&package_json)?;
    fs::write(&package_json_path, updated_content).await?;
    
    ui.success(&format!("✅ Version bumped to {}", new_version));
    
    // Create git tag if in git repository
    if is_git_repository(&current_dir).await {
        create_git_tag(&new_version.to_string(), ui).await?;
    }
    
    Ok(())
}

async fn interactive_version_bump(
    current_version: &Version,
    package_json: &mut PackageJson,
    package_json_path: &std::path::Path,
    ui: &UI,
) -> Result<()> {
    ui.info(&format!("Current version: {}", current_version));
    
    let patch_version = {
        let mut v = current_version.clone();
        v.patch += 1;
        v
    };
    
    let minor_version = {
        let mut v = current_version.clone();
        v.minor += 1;
        v.patch = 0;
        v
    };
    
    let major_version = {
        let mut v = current_version.clone();
        v.major += 1;
        v.minor = 0;
        v.patch = 0;
        v
    };
    
    println!("Select version bump:");
    println!("1. Patch ({})", patch_version);
    println!("2. Minor ({})", minor_version);
    println!("3. Major ({})", major_version);
    println!("4. Custom version");
    println!("5. Cancel");
    
    print!("Enter choice (1-5): ");
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    let new_version = match input.trim() {
        "1" => patch_version,
        "2" => minor_version,
        "3" => major_version,
        "4" => {
            print!("Enter custom version: ");
            io::stdout().flush()?;
            let mut custom_input = String::new();
            io::stdin().read_line(&mut custom_input)?;
            Version::parse(custom_input.trim())
                .context("Invalid version format")?
        }
        "5" | "" => {
            ui.info("Version bump cancelled");
            return Ok(());
        }
        _ => {
            ui.error("Invalid choice");
            return Ok(());
        }
    };
    
    // Update and save
    package_json.version = Some(new_version.to_string());
    let updated_content = serde_json::to_string_pretty(package_json)?;
    fs::write(package_json_path, updated_content).await?;
    
    ui.success(&format!("✅ Version bumped to {}", new_version));
    
    Ok(())
}

fn confirm_version_bump() -> Result<bool> {
    print!("Proceed with version bump? (y/N): ");
    io::stdout().flush()?;
    
    let mut input = String::new();
    io::stdin().read_line(&mut input)?;
    
    Ok(input.trim().to_lowercase().starts_with('y'))
}

async fn is_git_repository(dir: &std::path::Path) -> bool {
    dir.join(".git").exists()
}

async fn create_git_tag(version: &str, ui: &UI) -> Result<()> {
    let tag_name = format!("v{}", version);
    
    // Check if tag already exists
    let check_output = tokio::process::Command::new("git")
        .args(&["tag", "-l", &tag_name])
        .output()
        .await?;
    
    if !String::from_utf8_lossy(&check_output.stdout).trim().is_empty() {
        ui.warning(&format!("Git tag {} already exists", tag_name));
        return Ok(());
    }
    
    // Create tag
    let output = tokio::process::Command::new("git")
        .args(&["tag", "-a", &tag_name, "-m", &format!("Release {}", version)])
        .output()
        .await?;
    
    if output.status.success() {
        ui.success(&format!("✅ Created git tag: {}", tag_name));
    } else {
        ui.warning(&format!("Failed to create git tag: {}", String::from_utf8_lossy(&output.stderr)));
    }
    
    Ok(())
}
