use crate::cache::Cache;
use crate::types::ResolvedPackage;
use crate::ui::UI;
use anyhow::{Context, Result};
use flate2::read::GzDecoder;
use futures::stream::{self, StreamExt};
use reqwest::Client;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tar::Archive;
use tokio::fs;
use tokio::io::AsyncWriteExt;
use std::collections::HashMap;

pub struct Installer {
    client: Client,
    cache: Cache,
    node_modules_path: PathBuf,
    bin_path: PathBuf,
}

impl Installer {
    pub fn new(project_root: &Path) -> Result<Self> {
        let client = Client::builder()
            .pool_max_idle_per_host(20)
            .pool_idle_timeout(std::time::Duration::from_secs(30))
            .timeout(std::time::Duration::from_secs(60))
            .build()
            .context("Failed to create HTTP client")?;

        let node_modules_path = project_root.join("node_modules");
        let bin_path = node_modules_path.join(".bin");

        Ok(Self {
            client,
            cache: Cache::new()?,
            node_modules_path,
            bin_path,
        })
    }

    pub async fn install_packages(&self, packages: &[ResolvedPackage], ui: &UI) -> Result<()> {
        // Create node_modules directory
        fs::create_dir_all(&self.node_modules_path).await
            .context("Failed to create node_modules directory")?;
        
        fs::create_dir_all(&self.bin_path).await
            .context("Failed to create .bin directory")?;

        let install_progress = ui.create_install_progress(packages.len());
        
        // Install packages in parallel (limited concurrency)
        let results: Vec<Result<()>> = stream::iter(packages)
            .map(|package| {
                let installer = self;
                let progress = &install_progress;
                async move {
                    let result = installer.install_single_package(package, ui).await;
                    progress.inc(1);
                    result
                }
            })
            .buffer_unordered(10) // Limit concurrent installations
            .collect()
            .await;

        install_progress.finish_with_message("📦 Installation complete");

        // Check for errors
        for result in results {
            result?;
        }

        // Create bin links after all packages are installed
        self.create_bin_links(packages).await?;

        Ok(())
    }

    async fn install_single_package(&self, package: &ResolvedPackage, ui: &UI) -> Result<()> {
        let package_dir = self.get_package_directory(&package.name);
        
        // Skip if already installed with same version
        if self.is_package_installed(&package_dir, &package.version).await? {
            return Ok(());
        }

        // Handle different package sources
        match self.get_package_source(&package.resolved) {
            PackageSource::Registry => {
                self.install_from_registry(package, &package_dir, ui).await?;
            }
            PackageSource::Git => {
                self.install_from_git(package, &package_dir, ui).await?;
            }
            PackageSource::File => {
                self.install_from_file(package, &package_dir, ui).await?;
            }
            PackageSource::Url => {
                self.install_from_url(package, &package_dir, ui).await?;
            }
        }

        // Write package metadata
        self.write_package_metadata(&package_dir, package).await?;

        Ok(())
    }

    async fn install_from_registry(&self, package: &ResolvedPackage, package_dir: &Path, ui: &UI) -> Result<()> {
        // Download tarball
        let tarball_data = self.download_tarball(&package.resolved, &package.name, ui).await?;
        
        // Extract to package directory
        self.extract_tarball(&tarball_data, package_dir).await?;
        
        Ok(())
    }

    async fn install_from_git(&self, package: &ResolvedPackage, package_dir: &Path, _ui: &UI) -> Result<()> {
        // Clone git repository
        let git_url = &package.resolved;
        
        // Remove existing directory
        if package_dir.exists() {
            fs::remove_dir_all(package_dir).await?;
        }
        
        // Use git command to clone
        let output = tokio::process::Command::new("git")
            .args(&["clone", git_url, &package_dir.to_string_lossy()])
            .output()
            .await
            .context("Failed to clone git repository")?;
        
        if !output.status.success() {
            return Err(anyhow::anyhow!("Git clone failed: {}", String::from_utf8_lossy(&output.stderr)));
        }
        
        // Remove .git directory to save space
        let git_dir = package_dir.join(".git");
        if git_dir.exists() {
            fs::remove_dir_all(&git_dir).await?;
        }
        
        Ok(())
    }

    async fn install_from_file(&self, package: &ResolvedPackage, package_dir: &Path, _ui: &UI) -> Result<()> {
        let file_path = package.resolved.strip_prefix("file:").unwrap_or(&package.resolved);
        let source_path = Path::new(file_path);
        
        if !source_path.exists() {
            return Err(anyhow::anyhow!("File path does not exist: {}", file_path));
        }
        
        // Remove existing directory
        if package_dir.exists() {
            fs::remove_dir_all(package_dir).await?;
        }
        
        // Copy directory recursively
        self.copy_dir_recursive(source_path, package_dir).await?;
        
        Ok(())
    }

    async fn install_from_url(&self, package: &ResolvedPackage, package_dir: &Path, ui: &UI) -> Result<()> {
        // Download from URL
        let tarball_data = self.download_tarball(&package.resolved, &package.name, ui).await?;
        
        // Extract to package directory
        self.extract_tarball(&tarball_data, package_dir).await?;
        
        Ok(())
    }

    async fn download_tarball(&self, url: &str, package_name: &str, ui: &UI) -> Result<Vec<u8>> {
        let cache_key = format!("tarball:{}", url);
        
        // Try cache first
        if let Some(cached) = self.cache.get(&cache_key).await? {
            return Ok(cached);
        }

        // Download with progress bar
        let response = self.client.get(url)
            .send()
            .await
            .context("Failed to download package")?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("Failed to download {}: HTTP {}", package_name, response.status()));
        }

        let total_size = response.content_length().unwrap_or(0);
        let progress_bar = ui.create_progress_bar(total_size, &format!("{}@latest", package_name));

        let mut data = Vec::new();
        let mut stream = response.bytes_stream();
        
        while let Some(chunk) = stream.next().await {
            let chunk = chunk.context("Failed to read chunk")?;
            data.extend_from_slice(&chunk);
            progress_bar.inc(chunk.len() as u64);
        }

        progress_bar.finish_with_message(&format!("✅ Downloaded {}", package_name));

        // Cache the tarball
        self.cache.set(&cache_key, data.clone()).await?;

        Ok(data)
    }

    async fn extract_tarball(&self, data: &[u8], target_dir: &Path) -> Result<()> {
        // Remove existing directory
        if target_dir.exists() {
            fs::remove_dir_all(target_dir).await
                .context("Failed to remove existing package directory")?;
        }

        fs::create_dir_all(target_dir).await
            .context("Failed to create package directory")?;

        // Extract tarball
        let decoder = GzDecoder::new(data);
        let mut archive = Archive::new(decoder);

        // Use blocking task for tar extraction
        let target_dir = target_dir.to_owned();
        tokio::task::spawn_blocking(move || {
            for entry in archive.entries()? {
                let mut entry = entry?;
                let path = entry.path()?;
                
                // Skip the top-level "package/" directory
                let relative_path = path.strip_prefix("package").unwrap_or(&path);
                let target_path = target_dir.join(relative_path);

                if let Some(parent) = target_path.parent() {
                    std::fs::create_dir_all(parent)?;
                }

                entry.unpack(&target_path)?;
            }
            Ok::<(), anyhow::Error>(())
        }).await??;

        Ok(())
    }

    async fn copy_dir_recursive(&self, src: &Path, dst: &Path) -> Result<()> {
        fs::create_dir_all(dst).await?;
        
        let mut entries = fs::read_dir(src).await?;
        while let Some(entry) = entries.next_entry().await? {
            let src_path = entry.path();
            let dst_path = dst.join(entry.file_name());
            
            if src_path.is_dir() {
                self.copy_dir_recursive(&src_path, &dst_path).await?;
            } else {
                fs::copy(&src_path, &dst_path).await?;
            }
        }
        
        Ok(())
    }

    async fn create_bin_links(&self, packages: &[ResolvedPackage]) -> Result<()> {
        for package in packages {
            let package_dir = self.get_package_directory(&package.name);
            let package_json_path = package_dir.join("package.json");
            
            if !package_json_path.exists() {
                continue;
            }
            
            let content = fs::read_to_string(&package_json_path).await?;
            let package_json: serde_json::Value = serde_json::from_str(&content)?;
            
            // Handle bin field
            if let Some(bin) = package_json.get("bin") {
                match bin {
                    serde_json::Value::String(bin_path) => {
                        // Single binary with package name
                        self.create_bin_link(&package.name, &package_dir.join(bin_path)).await?;
                    }
                    serde_json::Value::Object(bin_map) => {
                        // Multiple binaries
                        for (bin_name, bin_path) in bin_map {
                            if let Some(bin_path_str) = bin_path.as_str() {
                                self.create_bin_link(bin_name, &package_dir.join(bin_path_str)).await?;
                            }
                        }
                    }
                    _ => {}
                }
            }
        }
        
        Ok(())
    }

    async fn create_bin_link(&self, bin_name: &str, bin_source: &Path) -> Result<()> {
        if !bin_source.exists() {
            return Ok(); // Skip if binary doesn't exist
        }
        
        let bin_link = self.bin_path.join(bin_name);
        
        // Remove existing link
        if bin_link.exists() {
            fs::remove_file(&bin_link).await?;
        }
        
        #[cfg(unix)]
        {
            // Create shell script wrapper on Unix
            let wrapper_content = format!(
                "#!/bin/sh\nnode \"{}\" \"$@\"\n",
                bin_source.display()
            );
            fs::write(&bin_link, wrapper_content).await?;
            
            // Make executable
            use std::os::unix::fs::PermissionsExt;
            let mut perms = fs::metadata(&bin_link).await?.permissions();
            perms.set_mode(0o755);
            fs::set_permissions(&bin_link, perms).await?;
        }
        
        #[cfg(windows)]
        {
            // Create batch file wrapper on Windows
            let wrapper_content = format!(
                "@echo off\nnode \"{}\" %*\n",
                bin_source.display()
            );
            let bat_link = self.bin_path.join(format!("{}.cmd", bin_name));
            fs::write(&bat_link, wrapper_content).await?;
        }
        
        Ok(())
    }

    fn get_package_directory(&self, package_name: &str) -> PathBuf {
        if package_name.starts_with('@') {
            // Scoped package
            let parts: Vec<&str> = package_name.splitn(2, '/').collect();
            if parts.len() == 2 {
                return self.node_modules_path.join(parts[0]).join(parts[1]);
            }
        }
        
        self.node_modules_path.join(package_name)
    }

    fn get_package_source(&self, resolved: &str) -> PackageSource {
        if resolved.starts_with("git+") || resolved.contains("github.com") || resolved.contains(".git") {
            PackageSource::Git
        } else if resolved.starts_with("file:") || resolved.starts_with("./") || resolved.starts_with("../") {
            PackageSource::File
        } else if resolved.starts_with("http://") || resolved.starts_with("https://") {
            if resolved.contains("registry.npmjs.org") {
                PackageSource::Registry
            } else {
                PackageSource::Url
            }
        } else {
            PackageSource::Registry
        }
    }

    async fn write_package_metadata(&self, package_dir: &Path, package: &ResolvedPackage) -> Result<()> {
        let metadata = serde_json::json!({
            "_resolved": package.resolved,
            "_integrity": package.integrity,
            "_from": format!("{}@{}", package.name, package.version),
            "_id": format!("{}@{}", package.name, package.version),
            "_nodeVersion": env!("CARGO_PKG_VERSION"),
            "_npmVersion": "nx-1.0.0"
        });

        let metadata_path = package_dir.join(".nx-metadata.json");
        let mut file = fs::File::create(&metadata_path).await
            .context("Failed to create metadata file")?;
        
        file.write_all(serde_json::to_string_pretty(&metadata)?.as_bytes()).await
            .context("Failed to write metadata")?;

        Ok(())
    }

    async fn is_package_installed(&self, package_dir: &Path, version: &str) -> Result<bool> {
        if !package_dir.exists() {
            return Ok(false);
        }

        let package_json_path = package_dir.join("package.json");
        if !package_json_path.exists() {
            return Ok(false);
        }

        let content = fs::read_to_string(&package_json_path).await?;
        let package_json: serde_json::Value = serde_json::from_str(&content)?;
        
        if let Some(installed_version) = package_json.get("version").and_then(|v| v.as_str()) {
            return Ok(installed_version == version);
        }

        Ok(false)
    }
}

#[derive(Debug)]
enum PackageSource {
    Registry,
    Git,
    File,
    Url,
}
