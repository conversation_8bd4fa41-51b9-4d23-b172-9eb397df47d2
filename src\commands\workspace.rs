use crate::types::PackageJson;
use crate::ui::UI;
use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use std::path::{Path, PathBuf};
use tokio::fs;

#[derive(Debug, Serialize, Deserialize)]
struct WorkspaceConfig {
    workspaces: Vec<String>,
    #[serde(rename = "workspaces-config")]
    config: Option<WorkspaceSettings>,
}

#[derive(Debug, Serialize, Deserialize)]
struct WorkspaceSettings {
    #[serde(rename = "hoist-pattern")]
    hoist_pattern: Option<Vec<String>>,
    #[serde(rename = "nohoist-pattern")]
    nohoist_pattern: Option<Vec<String>>,
}

pub async fn run_list(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let workspaces = find_workspaces(&current_dir).await?;
    
    if workspaces.is_empty() {
        ui.info("No workspaces found");
        return Ok();
    }
    
    let mut rows = Vec::new();
    for workspace in &workspaces {
        let package_json_path = workspace.join("package.json");
        if package_json_path.exists() {
            let content = fs::read_to_string(&package_json_path).await?;
            let package_json: PackageJson = serde_json::from_str(&content)?;
            
            let name = package_json.name.unwrap_or_else(|| "unnamed".to_string());
            let version = package_json.version.unwrap_or_else(|| "0.0.0".to_string());
            let location = workspace.strip_prefix(&current_dir)
                .unwrap_or(workspace)
                .display()
                .to_string();
            
            rows.push(vec![name, version, location]);
        }
    }
    
    ui.print_table(&["Name", "Version", "Location"], &rows);
    
    Ok(())
}

pub async fn run_run(script: String, workspace: Option<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let workspaces = find_workspaces(&current_dir).await?;
    
    if workspaces.is_empty() {
        return Err(anyhow::anyhow!("No workspaces found"));
    }
    
    let target_workspaces = if let Some(workspace_name) = workspace {
        // Run in specific workspace
        let workspace_path = find_workspace_by_name(&workspaces, &workspace_name).await?;
        vec![workspace_path]
    } else {
        // Run in all workspaces
        workspaces
    };
    
    for workspace_path in target_workspaces {
        let package_json_path = workspace_path.join("package.json");
        if !package_json_path.exists() {
            continue;
        }
        
        let content = fs::read_to_string(&package_json_path).await?;
        let package_json: PackageJson = serde_json::from_str(&content)?;
        
        let workspace_name = package_json.name.unwrap_or_else(|| "unnamed".to_string());
        
        if let Some(scripts) = &package_json.scripts {
            if let Some(command) = scripts.get(&script) {
                ui.info(&format!("🚀 Running '{}' in workspace '{}'", script, workspace_name));
                
                let status = tokio::process::Command::new("sh")
                    .args(&["-c", command])
                    .current_dir(&workspace_path)
                    .status()
                    .await
                    .context("Failed to execute script")?;
                
                if !status.success() {
                    ui.error(&format!("Script failed in workspace '{}'", workspace_name));
                    return Err(anyhow::anyhow!("Script execution failed"));
                }
                
                ui.success(&format!("✅ Script completed in workspace '{}'", workspace_name));
            } else {
                ui.warning(&format!("Script '{}' not found in workspace '{}'", script, workspace_name));
            }
        }
    }
    
    Ok(())
}

pub async fn run_install(workspace: Option<String>, ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let workspaces = find_workspaces(&current_dir).await?;
    
    if workspaces.is_empty() {
        return Err(anyhow::anyhow!("No workspaces found"));
    }
    
    let target_workspaces = if let Some(workspace_name) = workspace {
        let workspace_path = find_workspace_by_name(&workspaces, &workspace_name).await?;
        vec![workspace_path]
    } else {
        workspaces
    };
    
    // Install dependencies for each workspace
    for workspace_path in target_workspaces {
        let package_json_path = workspace_path.join("package.json");
        if !package_json_path.exists() {
            continue;
        }
        
        let content = fs::read_to_string(&package_json_path).await?;
        let package_json: PackageJson = serde_json::from_str(&content)?;
        let workspace_name = package_json.name.unwrap_or_else(|| "unnamed".to_string());
        
        ui.info(&format!("📦 Installing dependencies for workspace '{}'", workspace_name));
        
        // Change to workspace directory and install
        let original_dir = env::current_dir()?;
        env::set_current_dir(&workspace_path)?;
        
        // Use our install command
        crate::commands::install::run(vec![], false, false, false, ui).await?;
        
        env::set_current_dir(original_dir)?;
        
        ui.success(&format!("✅ Dependencies installed for workspace '{}'", workspace_name));
    }
    
    Ok(())
}

async fn find_workspaces(root_dir: &Path) -> Result<Vec<PathBuf>> {
    let mut workspaces = Vec::new();
    
    // Check for workspaces in package.json
    let package_json_path = root_dir.join("package.json");
    if package_json_path.exists() {
        let content = fs::read_to_string(&package_json_path).await?;
        let package_json: serde_json::Value = serde_json::from_str(&content)?;
        
        if let Some(workspace_patterns) = package_json.get("workspaces") {
            let patterns = match workspace_patterns {
                serde_json::Value::Array(arr) => {
                    arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>()
                }
                serde_json::Value::Object(obj) => {
                    if let Some(packages) = obj.get("packages") {
                        if let Some(arr) = packages.as_array() {
                            arr.iter().filter_map(|v| v.as_str()).collect::<Vec<_>>()
                        } else {
                            vec![]
                        }
                    } else {
                        vec![]
                    }
                }
                _ => vec![],
            };
            
            for pattern in patterns {
                let workspace_paths = glob_pattern(root_dir, pattern).await?;
                workspaces.extend(workspace_paths);
            }
        }
    }
    
    // Check for lerna.json
    let lerna_json_path = root_dir.join("lerna.json");
    if lerna_json_path.exists() {
        let content = fs::read_to_string(&lerna_json_path).await?;
        let lerna_config: serde_json::Value = serde_json::from_str(&content)?;
        
        if let Some(packages) = lerna_config.get("packages") {
            if let Some(arr) = packages.as_array() {
                for pattern in arr.iter().filter_map(|v| v.as_str()) {
                    let workspace_paths = glob_pattern(root_dir, pattern).await?;
                    workspaces.extend(workspace_paths);
                }
            }
        }
    }
    
    // Remove duplicates and sort
    workspaces.sort();
    workspaces.dedup();
    
    Ok(workspaces)
}

async fn glob_pattern(root_dir: &Path, pattern: &str) -> Result<Vec<PathBuf>> {
    let mut paths = Vec::new();
    
    // Simple glob implementation for common patterns
    if pattern.ends_with("/*") {
        let base_pattern = &pattern[..pattern.len() - 2];
        let base_path = root_dir.join(base_pattern);
        
        if base_path.exists() && base_path.is_dir() {
            let mut entries = fs::read_dir(&base_path).await?;
            while let Some(entry) = entries.next_entry().await? {
                let path = entry.path();
                if path.is_dir() && path.join("package.json").exists() {
                    paths.push(path);
                }
            }
        }
    } else {
        // Direct path
        let path = root_dir.join(pattern);
        if path.exists() && path.is_dir() && path.join("package.json").exists() {
            paths.push(path);
        }
    }
    
    Ok(paths)
}

async fn find_workspace_by_name(workspaces: &[PathBuf], name: &str) -> Result<PathBuf> {
    for workspace_path in workspaces {
        let package_json_path = workspace_path.join("package.json");
        if package_json_path.exists() {
            let content = fs::read_to_string(&package_json_path).await?;
            let package_json: PackageJson = serde_json::from_str(&content)?;
            
            if let Some(workspace_name) = &package_json.name {
                if workspace_name == name {
                    return Ok(workspace_path.clone());
                }
            }
        }
    }
    
    Err(anyhow::anyhow!("Workspace '{}' not found", name))
}
