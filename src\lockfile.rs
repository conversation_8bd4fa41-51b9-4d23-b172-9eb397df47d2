use crate::types::{LockFile, LockFilePackage, ResolvedPackage};
use anyhow::{Context, Result};
use std::collections::HashMap;
use std::path::Path;
use tokio::fs;

pub struct LockFileManager;

impl LockFileManager {
    pub async fn read_lockfile(path: &Path) -> Result<Option<LockFile>> {
        let lockfile_path = path.join("nx-lock.json");
        
        if !lockfile_path.exists() {
            // Try to read package-lock.json as fallback
            let npm_lockfile = path.join("package-lock.json");
            if npm_lockfile.exists() {
                return Self::read_npm_lockfile(&npm_lockfile).await;
            }
            return Ok(None);
        }

        let content = fs::read_to_string(&lockfile_path).await
            .context("Failed to read lockfile")?;
        
        let lockfile: LockFile = serde_json::from_str(&content)
            .context("Failed to parse lockfile")?;
        
        Ok(Some(lockfile))
    }

    pub async fn write_lockfile(path: &Path, packages: &[ResolvedPackage]) -> Result<()> {
        let mut lockfile_packages = HashMap::new();
        
        for package in packages {
            let key = format!("{}@{}", package.name, package.version);
            
            // Build dependency map
            let mut dependencies = HashMap::new();
            for dep in &package.dependencies {
                dependencies.insert(dep.name.clone(), dep.version.clone());
            }
            
            lockfile_packages.insert(key, LockFilePackage {
                resolved: package.resolved.clone(),
                integrity: package.integrity.clone(),
                dependencies: if dependencies.is_empty() { None } else { Some(dependencies) },
            });
        }

        let lockfile = LockFile {
            version: "1.0".to_string(),
            packages: lockfile_packages,
        };

        let lockfile_path = path.join("nx-lock.json");
        let content = serde_json::to_string_pretty(&lockfile)
            .context("Failed to serialize lockfile")?;
        
        fs::write(&lockfile_path, content).await
            .context("Failed to write lockfile")?;

        Ok(())
    }

    async fn read_npm_lockfile(path: &Path) -> Result<Option<LockFile>> {
        let content = fs::read_to_string(path).await
            .context("Failed to read package-lock.json")?;
        
        let npm_lockfile: serde_json::Value = serde_json::from_str(&content)
            .context("Failed to parse package-lock.json")?;
        
        // Convert npm lockfile format to nx format
        let mut nx_packages = HashMap::new();
        
        if let Some(packages) = npm_lockfile.get("packages").and_then(|p| p.as_object()) {
            for (path, package_info) in packages {
                if path.is_empty() {
                    continue; // Skip root package
                }
                
                if let (Some(name), Some(version)) = (
                    package_info.get("name").and_then(|n| n.as_str()),
                    package_info.get("version").and_then(|v| v.as_str())
                ) {
                    let key = format!("{}@{}", name, version);
                    
                    let resolved = package_info.get("resolved")
                        .and_then(|r| r.as_str())
                        .unwrap_or("")
                        .to_string();
                    
                    let integrity = package_info.get("integrity")
                        .and_then(|i| i.as_str())
                        .unwrap_or("")
                        .to_string();
                    
                    nx_packages.insert(key, LockFilePackage {
                        resolved,
                        integrity,
                        dependencies: None, // Could be extracted from npm lockfile if needed
                    });
                }
            }
        }
        
        Ok(Some(LockFile {
            version: "1.0".to_string(),
            packages: nx_packages,
        }))
    }

    pub async fn install_from_lockfile(path: &Path, ui: &crate::ui::UI) -> Result<()> {
        let lockfile = Self::read_lockfile(path).await?
            .ok_or_else(|| anyhow::anyhow!("No lockfile found"))?;
        
        ui.info(&format!("📦 Installing {} packages from lockfile...", lockfile.packages.len()));
        
        // Convert lockfile packages to resolved packages
        let mut resolved_packages = Vec::new();
        
        for (key, package_info) in &lockfile.packages {
            // Parse package name and version from key
            if let Some(at_pos) = key.rfind('@') {
                let name = key[..at_pos].to_string();
                let version = key[at_pos + 1..].to_string();
                
                resolved_packages.push(ResolvedPackage {
                    name,
                    version,
                    resolved: package_info.resolved.clone(),
                    integrity: package_info.integrity.clone(),
                    dependencies: Vec::new(), // Dependencies would be resolved separately
                });
            }
        }
        
        // Install packages
        let installer = crate::installer::Installer::new(path)?;
        installer.install_packages(&resolved_packages, ui).await?;
        
        Ok(())
    }
}
