use crate::ui::UI;
use anyhow::{Context, Result};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::env;
use tokio::fs;

#[derive(Debug, Serialize, Deserialize)]
struct SecurityReport {
    vulnerabilities: Vec<Vulnerability>,
    summary: SecuritySummary,
}

#[derive(Debug, Serialize, Deserialize)]
struct Vulnerability {
    id: String,
    title: String,
    severity: String,
    package_name: String,
    vulnerable_versions: String,
    patched_versions: Option<String>,
    cwe: Vec<String>,
    cvss_score: Option<f64>,
    description: String,
    references: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct SecuritySummary {
    total: u32,
    critical: u32,
    high: u32,
    moderate: u32,
    low: u32,
    info: u32,
}

pub async fn run_scan(ui: &UI) -> Result<()> {
    let current_dir = env::current_dir().context("Failed to get current directory")?;
    let package_json_path = current_dir.join("package.json");
    
    if !package_json_path.exists() {
        return Err(anyhow::anyhow!("No package.json found"));
    }
    
    let spinner = ui.create_spinner("🔍 Scanning for security vulnerabilities...");
    
    let content = fs::read_to_string(&package_json_path).await?;
    let package_json: crate::types::PackageJson = serde_json::from_str(&content)?;
    
    let mut all_packages = HashMap::new();
    
    if let Some(deps) = &package_json.dependencies {
        all_packages.extend(deps.clone());
    }
    
    if let Some(dev_deps) = &package_json.dev_dependencies {
        all_packages.extend(dev_deps.clone());
    }
    
    let vulnerabilities = scan_packages(&all_packages).await?;
    
    spinner.finish_with_message("🔍 Security scan complete");
    
    let summary = generate_summary(&vulnerabilities);
    
    display_security_report(&vulnerabilities, &summary, ui);
    
    // Generate security report file
    generate_security_report(&vulnerabilities, &summary, &current_dir).await?;
    
    Ok(())
}

pub async fn run_check_package(package_name: String, ui: &UI) -> Result<()> {
    let spinner = ui.create_spinner(&format!("🔍 Checking security for {}...", package_name));
    
    let vulnerabilities = check_single_package(&package_name).await?;
    
    spinner.finish_with_message(&format!("🔍 Security check complete for {}", package_name));
    
    if vulnerabilities.is_empty() {
        ui.success(&format!("✅ No known vulnerabilities found for {}", package_name));
    } else {
        ui.warning(&format!("⚠️ Found {} vulnerabilities for {}", vulnerabilities.len(), package_name));
        
        for vuln in &vulnerabilities {
            display_vulnerability(vuln, ui);
        }
    }
    
    Ok(())
}

pub async fn run_update_db(ui: &UI) -> Result<()> {
    let spinner = ui.create_spinner("📥 Updating vulnerability database...");
    
    // Download latest vulnerability database
    let client = Client::new();
    let response = client
        .get("https://registry.npmjs.org/-/npm/v1/security/advisories")
        .send()
        .await
        .context("Failed to download vulnerability database")?;
    
    if !response.status().is_success() {
        return Err(anyhow::anyhow!("Failed to download vulnerability database"));
    }
    
    let vuln_data = response.bytes().await?;
    
    // Store vulnerability database
    let cache_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx")
        .join("security");
    
    fs::create_dir_all(&cache_dir).await?;
    
    let db_file = cache_dir.join("vulnerabilities.json");
    fs::write(&db_file, vuln_data).await?;
    
    spinner.finish_with_message("📥 Vulnerability database updated");
    
    ui.success("✅ Security database updated successfully");
    
    Ok(())
}

async fn scan_packages(packages: &HashMap<String, String>) -> Result<Vec<Vulnerability>> {
    let mut vulnerabilities = Vec::new();
    
    for (package_name, _version) in packages {
        let package_vulns = check_single_package(package_name).await?;
        vulnerabilities.extend(package_vulns);
    }
    
    Ok(vulnerabilities)
}

async fn check_single_package(package_name: &str) -> Result<Vec<Vulnerability>> {
    // Check against local vulnerability database first
    if let Ok(local_vulns) = check_local_vulnerability_db(package_name).await {
        if !local_vulns.is_empty() {
            return Ok(local_vulns);
        }
    }
    
    // Fallback to online check
    check_online_vulnerabilities(package_name).await
}

async fn check_local_vulnerability_db(package_name: &str) -> Result<Vec<Vulnerability>> {
    let cache_dir = dirs::home_dir()
        .context("Could not find home directory")?
        .join(".nx")
        .join("security");
    
    let db_file = cache_dir.join("vulnerabilities.json");
    
    if !db_file.exists() {
        return Ok(Vec::new());
    }
    
    let content = fs::read_to_string(&db_file).await?;
    let all_vulns: HashMap<String, Vec<Vulnerability>> = serde_json::from_str(&content)?;
    
    Ok(all_vulns.get(package_name).cloned().unwrap_or_default())
}

async fn check_online_vulnerabilities(package_name: &str) -> Result<Vec<Vulnerability>> {
    let client = Client::new();
    let response = client
        .get(&format!("https://registry.npmjs.org/-/npm/v1/security/advisories?package={}", package_name))
        .send()
        .await;
    
    match response {
        Ok(resp) if resp.status().is_success() => {
            let vulns: Vec<Vulnerability> = resp.json().await.unwrap_or_default();
            Ok(vulns)
        }
        _ => {
            // Return known vulnerabilities for common packages
            Ok(get_known_vulnerabilities(package_name))
        }
    }
}

fn get_known_vulnerabilities(package_name: &str) -> Vec<Vulnerability> {
    // Database of known vulnerabilities for common packages
    match package_name {
        "lodash" => vec![
            Vulnerability {
                id: "GHSA-35jh-r3h4-6jhm".to_string(),
                title: "Prototype Pollution in lodash".to_string(),
                severity: "high".to_string(),
                package_name: "lodash".to_string(),
                vulnerable_versions: "<4.17.21".to_string(),
                patched_versions: Some(">=4.17.21".to_string()),
                cwe: vec!["CWE-1321".to_string()],
                cvss_score: Some(7.5),
                description: "Lodash versions prior to 4.17.21 are vulnerable to Prototype Pollution".to_string(),
                references: vec!["https://github.com/advisories/GHSA-35jh-r3h4-6jhm".to_string()],
            }
        ],
        "minimist" => vec![
            Vulnerability {
                id: "GHSA-xvch-5gv4-984h".to_string(),
                title: "Prototype Pollution in minimist".to_string(),
                severity: "moderate".to_string(),
                package_name: "minimist".to_string(),
                vulnerable_versions: "<1.2.6".to_string(),
                patched_versions: Some(">=1.2.6".to_string()),
                cwe: vec!["CWE-1321".to_string()],
                cvss_score: Some(5.6),
                description: "Minimist before 1.2.6 is vulnerable to Prototype Pollution".to_string(),
                references: vec!["https://github.com/advisories/GHSA-xvch-5gv4-984h".to_string()],
            }
        ],
        _ => Vec::new(),
    }
}

fn generate_summary(vulnerabilities: &[Vulnerability]) -> SecuritySummary {
    let mut summary = SecuritySummary {
        total: vulnerabilities.len() as u32,
        critical: 0,
        high: 0,
        moderate: 0,
        low: 0,
        info: 0,
    };
    
    for vuln in vulnerabilities {
        match vuln.severity.as_str() {
            "critical" => summary.critical += 1,
            "high" => summary.high += 1,
            "moderate" => summary.moderate += 1,
            "low" => summary.low += 1,
            "info" => summary.info += 1,
            _ => {}
        }
    }
    
    summary
}

fn display_security_report(vulnerabilities: &[Vulnerability], summary: &SecuritySummary, ui: &UI) {
    if summary.total == 0 {
        ui.success("✅ No security vulnerabilities found!");
        return;
    }
    
    ui.warning(&format!("⚠️ Found {} security vulnerabilities", summary.total));
    
    if summary.critical > 0 {
        ui.error(&format!("  🔴 {} critical", summary.critical));
    }
    if summary.high > 0 {
        ui.error(&format!("  🟠 {} high", summary.high));
    }
    if summary.moderate > 0 {
        ui.warning(&format!("  🟡 {} moderate", summary.moderate));
    }
    if summary.low > 0 {
        ui.info(&format!("  🔵 {} low", summary.low));
    }
    if summary.info > 0 {
        ui.info(&format!("  ℹ️ {} info", summary.info));
    }
    
    println!("\nTop vulnerabilities:");
    let mut sorted_vulns = vulnerabilities.to_vec();
    sorted_vulns.sort_by(|a, b| {
        let severity_order = |s: &str| match s {
            "critical" => 0,
            "high" => 1,
            "moderate" => 2,
            "low" => 3,
            _ => 4,
        };
        severity_order(&a.severity).cmp(&severity_order(&b.severity))
    });
    
    for (i, vuln) in sorted_vulns.iter().take(5).enumerate() {
        display_vulnerability_summary(i + 1, vuln);
    }
    
    ui.info("Run 'nx security scan --detailed' for full report");
}

fn display_vulnerability(vuln: &Vulnerability, ui: &UI) {
    let severity_icon = match vuln.severity.as_str() {
        "critical" => "🔴",
        "high" => "🟠",
        "moderate" => "🟡",
        "low" => "🔵",
        _ => "ℹ️",
    };
    
    println!("{} {} - {} ({})", 
             severity_icon, 
             vuln.severity.to_uppercase(), 
             vuln.title,
             vuln.package_name);
    
    println!("  ID: {}", vuln.id);
    println!("  Vulnerable: {}", vuln.vulnerable_versions);
    
    if let Some(patched) = &vuln.patched_versions {
        println!("  Patched: {}", patched);
    }
    
    if let Some(cvss) = vuln.cvss_score {
        println!("  CVSS Score: {}", cvss);
    }
    
    println!("  Description: {}", vuln.description);
    println!();
}

fn display_vulnerability_summary(index: usize, vuln: &Vulnerability) {
    let severity_icon = match vuln.severity.as_str() {
        "critical" => "🔴",
        "high" => "🟠",
        "moderate" => "🟡",
        "low" => "🔵",
        _ => "ℹ️",
    };
    
    println!("{}. {} {} - {} ({})", 
             index,
             severity_icon, 
             vuln.severity.to_uppercase(), 
             vuln.title,
             vuln.package_name);
}

async fn generate_security_report(
    vulnerabilities: &[Vulnerability], 
    summary: &SecuritySummary, 
    project_dir: &std::path::Path
) -> Result<()> {
    let report = SecurityReport {
        vulnerabilities: vulnerabilities.to_vec(),
        summary: summary.clone(),
    };
    
    let report_content = serde_json::to_string_pretty(&report)?;
    let report_file = project_dir.join("nx-security-report.json");
    
    fs::write(&report_file, report_content).await?;
    
    // Also generate human-readable report
    let mut markdown_report = String::new();
    markdown_report.push_str("# Security Report\n\n");
    markdown_report.push_str(&format!("Generated: {}\n\n", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")));
    
    markdown_report.push_str("## Summary\n\n");
    markdown_report.push_str(&format!("- Total vulnerabilities: {}\n", summary.total));
    markdown_report.push_str(&format!("- Critical: {}\n", summary.critical));
    markdown_report.push_str(&format!("- High: {}\n", summary.high));
    markdown_report.push_str(&format!("- Moderate: {}\n", summary.moderate));
    markdown_report.push_str(&format!("- Low: {}\n", summary.low));
    markdown_report.push_str(&format!("- Info: {}\n\n", summary.info));
    
    if !vulnerabilities.is_empty() {
        markdown_report.push_str("## Vulnerabilities\n\n");
        
        for vuln in vulnerabilities {
            markdown_report.push_str(&format!("### {} - {}\n\n", vuln.id, vuln.title));
            markdown_report.push_str(&format!("- **Package**: {}\n", vuln.package_name));
            markdown_report.push_str(&format!("- **Severity**: {}\n", vuln.severity));
            markdown_report.push_str(&format!("- **Vulnerable versions**: {}\n", vuln.vulnerable_versions));
            
            if let Some(patched) = &vuln.patched_versions {
                markdown_report.push_str(&format!("- **Patched versions**: {}\n", patched));
            }
            
            if let Some(cvss) = vuln.cvss_score {
                markdown_report.push_str(&format!("- **CVSS Score**: {}\n", cvss));
            }
            
            markdown_report.push_str(&format!("- **Description**: {}\n", vuln.description));
            
            if !vuln.references.is_empty() {
                markdown_report.push_str("- **References**:\n");
                for reference in &vuln.references {
                    markdown_report.push_str(&format!("  - {}\n", reference));
                }
            }
            
            markdown_report.push_str("\n");
        }
    }
    
    let markdown_file = project_dir.join("nx-security-report.md");
    fs::write(&markdown_file, markdown_report).await?;
    
    Ok(())
}
