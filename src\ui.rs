use colored::*;
use crossterm::{
    cursor,
    terminal::{self, ClearType},
    ExecutableCommand,
};
use indicatif::{MultiProgress, ProgressBar, ProgressStyle};
use std::io::{self, Write};
use std::sync::Arc;
use std::time::Duration;

pub struct UI {
    multi_progress: Arc<MultiProgress>,
}

impl UI {
    pub fn new() -> Self {
        Self {
            multi_progress: Arc::new(MultiProgress::new()),
        }
    }

    pub fn success(&self, message: &str) {
        println!("{} {}", "✅".green(), message.green());
    }

    pub fn error(&self, message: &str) {
        eprintln!("{} {}", "❌".red(), message.red());
    }

    pub fn warning(&self, message: &str) {
        println!("{} {}", "⚠️".yellow(), message.yellow());
    }

    pub fn info(&self, message: &str) {
        println!("{} {}", "ℹ️".blue(), message);
    }

    pub fn create_spinner(&self, message: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new_spinner());
        pb.set_style(
            ProgressStyle::default_spinner()
                .tick_chars("⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏")
                .template("{spinner:.blue} {msg}")
                .unwrap(),
        );
        pb.set_message(message.to_string());
        pb.enable_steady_tick(Duration::from_millis(100));
        pb
    }

    pub fn create_progress_bar(&self, len: u64, message: &str) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(len));
        pb.set_style(
            ProgressStyle::default_bar()
                .template("📥 {msg} [{bar:40.cyan/blue}] {percent}% | {bytes}/{total_bytes} | {bytes_per_sec} | ETA {eta}")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏ "),
        );
        pb.set_message(message.to_string());
        pb
    }

    pub fn create_install_progress(&self, total: usize) -> ProgressBar {
        let pb = self.multi_progress.add(ProgressBar::new(total as u64));
        pb.set_style(
            ProgressStyle::default_bar()
                .template("📦 Installing [{bar:40.green}] {pos}/{len} packages")
                .unwrap()
                .progress_chars("█▉▊▋▌▍▎▏ "),
        );
        pb
    }

    pub fn print_table(&self, headers: &[&str], rows: &[Vec<String>]) {
        if rows.is_empty() {
            return;
        }

        // Calculate column widths
        let mut widths = headers.iter().map(|h| h.len()).collect::<Vec<_>>();
        for row in rows {
            for (i, cell) in row.iter().enumerate() {
                if i < widths.len() {
                    widths[i] = widths[i].max(cell.len());
                }
            }
        }

        // Print top border
        print!("┌");
        for (i, &width) in widths.iter().enumerate() {
            print!("{}", "─".repeat(width + 2));
            if i < widths.len() - 1 {
                print!("┬");
            }
        }
        println!("┐");

        // Print headers
        print!("│");
        for (i, (header, &width)) in headers.iter().zip(&widths).enumerate() {
            print!(" {:<width$} ", header, width = width);
            if i < headers.len() - 1 {
                print!("│");
            }
        }
        println!("│");

        // Print separator
        print!("├");
        for (i, &width) in widths.iter().enumerate() {
            print!("{}", "─".repeat(width + 2));
            if i < widths.len() - 1 {
                print!("┼");
            }
        }
        println!("┤");

        // Print rows
        for row in rows {
            print!("│");
            for (i, (cell, &width)) in row.iter().zip(&widths).enumerate() {
                print!(" {:<width$} ", cell, width = width);
                if i < row.len() - 1 {
                    print!("│");
                }
            }
            println!("│");
        }

        // Print bottom border
        print!("└");
        for (i, &width) in widths.iter().enumerate() {
            print!("{}", "─".repeat(width + 2));
            if i < widths.len() - 1 {
                print!("┴");
            }
        }
        println!("┘");
    }

    pub fn print_package_info(&self, name: &str, version: &str, size: &str, license: &str, deps: usize) {
        println!("┌{}┐", "─".repeat(name.len() + 2));
        println!("│ {} │", name.bold());
        println!("├{}┬{}┤", "─".repeat(13), "─".repeat(name.len().max(13) - 11));
        println!("│ Version     │ {:<width$} │", version, width = name.len().max(13) - 13);
        println!("│ Dependencies│ {:<width$} │", deps, width = name.len().max(13) - 13);
        println!("│ Size        │ {:<width$} │", size, width = name.len().max(13) - 13);
        println!("│ License     │ {:<width$} │", license, width = name.len().max(13) - 13);
        println!("└{}┴{}┘", "─".repeat(13), "─".repeat(name.len().max(13) - 11));
    }

    pub fn clear_line(&self) {
        let mut stdout = io::stdout();
        stdout.execute(cursor::MoveToColumn(0)).unwrap();
        stdout.execute(terminal::Clear(ClearType::CurrentLine)).unwrap();
        stdout.flush().unwrap();
    }
}
